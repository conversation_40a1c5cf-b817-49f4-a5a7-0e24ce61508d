# DEFINICIONES DE PATRONES

- Los patrones de diseño (design patterns) son soluciones habituales a problemas comunes en el diseño de software. Cada patrón es como un plano que se puede personalizar para resolver un problema de diseño particular de tu código.
- Los patrones de diseño varían en su complejidad, nivel de detalle y escala de aplicabilidad al sistema completo que se diseña. Los *patrones creacionales* proporcionan mecanismos de creación de objetos que incrementan la flexibilidad y la reutilización de código existente. Los *patrones estructurales* explican cómo ensamblar objetos y clases en estructuras más grandes a la vez que se mantiene la flexibilidad y eficiencia de la estructura.
- Los *patrones de comportamiento* se encargan de una comunicación efectiva y la asignación de responsabilidades entre objetos.


## PATRONES CREACIONALES

- [https://refactoring.guru/es/design-patterns/singleton](Ver en refactoring.guru) *Singleton* es un patrón de diseño creacional que nos permite asegurarnos de que una clase tenga una única instancia, a la vez que proporciona un punto de acceso global a dicha instancia. Todas las implementaciones del patrón Singleton tienen estos dos pasos en común: Hacer privado el constructor por defecto para evitar que otros objetos utilicen el operador new con la clase Singleton. Crear un método de creación estático que actúe como constructor. Tras bambalinas, este método invoca al constructor privado para crear un objeto y lo guarda en un campo estático. Las siguientes llamadas a este método devuelven el objeto almacenado en caché.
- [https://refactoring.guru/es/design-patterns/factory-method](Ver en refactoring.guru) *Factory Method* es un patrón de diseño creacional que proporciona una interfaz para crear objetos en una superclase, mientras permite a las subclases alterar el tipo de objetos que se crearán. Imagina que escribes una aplicación utilizando un framework de UI de código abierto. Tu aplicación debe tener botones redondos, pero el framework sólo proporciona botones cuadrados. Extiendes la clase estándar Botón con una maravillosa subclase BotónRedondo, pero ahora tienes que decirle a la clase principal FrameworkUI que utilice la nueva subclase de botón en lugar de la clase por defecto. Para conseguirlo, creamos una subclase UIConBotonesRedondos a partir de una clase base del framework y sobrescribimos su método crearBotón. Si bien este método devuelve objetos Botón en la clase base, haces que tu subclase devuelva objetos BotónRedondo. Ahora, utiliza la clase UIConBotonesRedondos en lugar de FrameworkUI. ¡Eso es todo!
- [https://refactoring.guru/es/design-patterns/prototype](Ver en refactoring.guru) *Prototype* es un patrón de diseño creacional que nos permite copiar objetos existentes sin que el código dependa de sus clases. El patrón Prototype delega el proceso de clonación a los propios objetos que están siendo clonados. El patrón declara una interfaz común para todos los objetos que soportan la clonación. Esta interfaz nos permite clonar un objeto sin acoplar el código a la clase de ese objeto. Normalmente, dicha interfaz contiene un único método clonar.
- [https://refactoring.guru/es/design-patterns/abstract-factory](Ver en refactoring.guru) *Abstract Factory* es un patrón de diseño creacional que nos permite producir familias de objetos relacionados sin especificar sus clases concretas. Lo primero que sugiere el patrón Abstract Factory es que declaremos de forma explícita interfaces para cada producto diferente de la familia de productos (por ejemplo, silla, sofá o mesilla). Después podemos hacer que todas las variantes de los productos sigan esas interfaces. Por ejemplo, todas las variantes de silla pueden implementar la interfaz Silla, así como todas las variantes de mesilla pueden implementar la interfaz Mesilla, y así sucesivamente.