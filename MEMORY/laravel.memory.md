# DEVELOPER FRASES

- A user interface is like a joke, if you have to explain it, it's not that good.
- There's a well-known quote by author <PERSON>: "Eat food. Not too much. Mostly plants.". We can put it also like "Write code. Not too much. Mostly functions".
- Aprende a Aprender. Descubre la manera en que aprendes mejor. Desarrolla modelos mentales al rededor de cómo te gusta resolver problemas. Úsalos a discreción.
- Procura La Consistencia. En lo que haces y cómo lo haces. No importa el lenguaje, la tecnología o la metodología que uses. Te harás notar por la fiabilidad de tus contribuciones. Y nada es mejor para incrementar tu fiabilidad, que la constancia.
- Experimenta Resolviendo Problemas Sin Código. Antes de correr a escribir código, prueba descomponiendo el problema en una hoja de Excel. Usa una herramienta no-code para validar tu lógica. Escribir el código es relativamente sencillo si tienes bien claro lo que tienes que hacer.
- Favorece la Simplicidad Sobre Soluciones Brillantes. El código se lee muchas más veces de las que se escribe. Procura que no te gane la idea de que el código más complejo es inherentemente mejor. Menos es más.
- Conviértete en un Desarrollador 10X. Un desarrollador 10X no es aquel que hace el trabajo de otras 10 personas, sino aquel que ayuda a que 10 de sus compañeros hagan el mejor trabajo posible. No acapares. Ayuda.
- Busca Una Organización que Valga La Pena. Vas a hacer tu mejor trabajo en una organización que te deje: • Usar la tecnología que te gusta • Hacer el tipo de trabajo en el que te sientes más cómodo • Resolver problemas inspiran
- Sé Profesional, No Mercenario. Un profesional actúa con base en sus principios y ética de trabajo. Un mercenario actúa para el que pague más. El profesional da confianza. El mercenario la quita.
- Sal de Tu Zona de Confort. Una cosa es la consistencia. Otra es hacer exactamente lo mismo durante 10 años. Asegúrate de que vas modulando, poco a poco, la complejidad de los retos que tomas. Sube las apuestas. Sigue tus principios.
- A user interface is like a joke, if you have to explain it, it's not that good.

## ARTISAN MAINTENANCE MODE

php artisan down
php artisan down — refresh=15 -- message "Estamos actualizando"
php artisan down — secret=”1630542a-246b-4b66-afa1-dd72a4c43515"

## ARTISAN MIGRATE

php artisan schema:dump --prune
php artisan migrate:rollback --step=1

## FILAMENT ASSETS

php artisan filament:assets

## LIVEWIRE

wire:model.defer
wire:model.debounce:500ms
wire:model.debounce:1s

## QUEUES

php artisan queue:work --stop-when-empty --once --queue=tiempos
php artisan queue:work --queue=tiempos,default
php artisan queue:retry all
php artisan livewire:publish --assets

## MODELOS DESDE DB

Para crear modelos desde db (https://github.com/reliese/laravel):
- composer require reliese/laravel
- php artisan vendor:publish --tag=reliese-models
- php artisan code:models --table=datosxparticipantes
- php artisan vendor:publish --tag=reliese-models
- clear
- php artisan code:models --table=datosxparticipantes
- php artisan config:clear
- php artisan code:models --table=datosxparticipantes

## REFACTOR

- Restructuring a Laravel Controller using Services, Events, Jobs, Actions: https://laravel-news.com/controller-refactor