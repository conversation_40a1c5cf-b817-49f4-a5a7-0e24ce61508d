@extends('layout')

@section('content')

<form name="inscripcion" method="post" enctype="multipart/form-data">
    <section class="datos">

        @if ($evento['inscripciones_texto'])
            {!! html_entity_decode($evento['inscripciones_texto']) !!}
            <hr>
        @endif

        <span class="obligatorios"><b>*</b> Los campos marcados con asterísco son obligatorios</span>
        <br><br>

        <label for="mail">
            <span>Correo electrónico *</span>
            <input type="email" name="mail" placeholder="" value="{{ $post['mail'] }}" required="required" />
        </label>

        @if ($evento['tipo_nombre'] == 'libre')
            <label for="nombre">
                <span>Nombre/s y apellido/s *</span>
                <input type="text" name="nombre" placeholder="" value="{{ $post['nombre'] }}" required="required" />
            </label>
        @else
            <label for="nombre">
                <span>Nombre/s *</span>
                <input type="text" name="nombre" placeholder="" value="{{ $post['nombre'] }}" required="required" />
            </label>
            <label for="apellido">
                <span>Apellido/s *</span>
                <input type="text" name="apellido" placeholder="" value="{{ $post['apellido'] }}" required="required" />
            </label>
        @endif

        @if ($evento['nombre_localidad'])
        <label for="localidad">
            <span>{{ $evento['nombre_localidad'] }}</span>
            <input type="text" name="localidad" placeholder="" value="{{ $post['localidad'] }}" />
        </label>
        <hr>
        @endif

        @if ($categoria_sexo['mixto'] && !$categoria_sexo['femenino'] && !$categoria_sexo['masculino'])
            <input type="radio" name="sexo" value="masculino" checked style="display: none;" />
        @else
            <label for="sexo">
                <span>Género *</span><br>
                @if ($categoria_sexo['femenino'] || $categoria_sexo['mixto'])
                    <input type="radio" name="sexo" value="femenino" required="required" {{ (isset($post['sexo']) && $post['sexo'] == 'femenino') ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                    <label for="femenino">Femenino</label><br>
                @endif
                @if ($categoria_sexo['masculino'] || $categoria_sexo['mixto'])
                    <input type="radio" name="sexo" value="masculino" {{ (isset($post['sexo']) && $post['sexo'] == 'masculino') ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                    <label for="masculino">Masculino</label><br>
                @endif
                @if ($categoria_sexo['otro'])
                    <input type="radio" name="sexo" value="otro" {{ (isset($post['sexo']) && $post['sexo'] == 'otro') ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                    <label for="otro">Otro</label><br>
                @endif
            </label>
        @endif

        <label for="idcarrera">
            <span>Carrera *</span><br>
            @foreach ($carreras as $carrera)
                <input type="radio" name="idcarrera" value="{{ $carrera['idcarrera'] }}" {{ !$carrera['inscripciones'] ? 'disabled="disabled"' : '' }} {{ (isset($post['idcarrera']) && $post['idcarrera']) == $carrera['idcarrera'] ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                <label for="{{ $carrera['idcarrera'] }}">{{ $carrera['nombre'] }}
                    @if (!$carrera['inscripciones'])
                        <span style="color:red">SIN CUPO</span>
                    @endif
                </label><br>
            @endforeach
        </label>

        @if (array_key_exists('nacimiento', $datosxeventos))
            <label for="nacimiento">
                <span>Fecha de nacimiento *</span>
                <input type="date" id="nacimiento_datoxevento" name="nacimiento" step="1" value="{{ $post['nacimiento'] }}" {{ $datosxeventos['nacimiento']['obligatorio'] ? 'required="required"' : '' }} onchange="selectCategoria()" />
            </label>
        @endif

        <label for="idcategoria">
            <span>Categoría *</span>
            <select name="idcategoria" id="categorias" class="">
                <option value="">Seleccione el género y participación para ver las categorías correspondientes</option>
            </select>
        </label>
        <input type="hidden" id="idcategoria_post" name="idcategoria_post" value="{{ $post['idcategoria'] }}">

        <div id="agregarCategoria">
            <label for="agregarCategoria">
                <input type="hidden" id="idcategorias" name="idcategorias" value="{{ $post['idcategorias'] }}">
                <input type="button" value="Agregar Carrera y Categoría" onclick="agregarCategoria()" />
                <span class="categoriasSeleccionadas">Categorías seleccionadas:</span>
                <ul id="listaCategorias"></ul>
            </label>
        </div>

        <hr>

        @foreach ($datosxeventos as $datoxevento)
            @php
                $iddato = $datoxevento['iddato'];
                $post[$iddato] = $post[$iddato] ?? '';
                $required = $datoxevento['obligatorio'] ? 'required="required" ' : '';
            @endphp

            @if ($iddato == 'nacimiento')
                @continue
            @endif

            <label for="{{ $iddato }}">
                <span>{{ $datoxevento['nombre'] }} {{ $datoxevento['obligatorio'] ? '*' : '' }}
                    <i>{{ $iddato == $evento['iddato_unico'] ? '(Dato único entre todos los participantes)' : '' }}</i>
                </span>

                @switch($datoxevento['tipo'])
                    @case('text')
                    @case('number')
                        <input type="{{ $datoxevento['tipo'] }}" name="{{ $iddato }}" placeholder="{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}" value="{{ $post[$iddato] }}" {!! $required !!} />
                        @break

                    @case('date')
                        @if ($iddato != 'nacimiento')
                            @if ($datoxevento['observacion'] || $datoxevento['datos_observacion'])
                                <br><span class="label_observacion">{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}</span><br>
                            @endif
                            <input type="{{ $datoxevento['tipo'] }}" name="{{ $iddato }}" value="{{ $post[$iddato] }}" {!! $required !!} />
                        @endif
                        @break

                    @case('pregunta')
                        <br />
                        <input type="radio" name="{{ $iddato }}" value="SI" {!! $required !!} {{ $post[$iddato] == 'SI' ? 'checked="checked"' : '' }} /><label for="SI">SI</label>&nbsp;&nbsp;&nbsp;
                        <input type="radio" name="{{ $iddato }}" value="NO" {{ $post[$iddato] == 'NO' ? 'checked="checked"' : '' }} /><label for="NO">NO</label><br />
                        <input type="text" name="{{ $iddato }}_texto" placeholder="{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}" value="{{ $post[$iddato.'_texto'] }}" />
                        <br>
                        @break

                    @case('select')
                        <br />
                        <select name="{{ $iddato }}" {!! $required !!}>
                            <option value="">Seleccione una opción</option>
                            @php
                                $opciones = explode('|', ($datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion']));
                            @endphp
                            @foreach ($opciones as $opcion)
                                <option value="{{ $opcion }}" {{ $post[$iddato] == $opcion ? 'selected="selected"' : '' }}>{{ $opcion }}</option>
                            @endforeach
                        </select>
                        @break

                    @case('acepta')
                        <br />
                        <input type="radio" name="{{ $iddato }}" value="SI" {!! $required !!} {{ $post[$iddato] == 'SI' ? 'checked="checked"' : '' }} /><label for="SI">SI</label>&nbsp;&nbsp;&nbsp;
                        <p class="reglamento">{{ $datoxevento['observacion'] }}</p>
                        <br>
                        @break

                    @case('file')
                        @if ($datoxevento['observacion'] || $datoxevento['datos_observacion'])
                            <br><span class="label_observacion">{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}</span><br>
                        @endif
                        <input type="{{ $datoxevento['tipo'] }}" name="{{ $iddato }}" value="{{ $post[$iddato] }}" {!! $required !!} accept="image/jpeg, image/png, image/x-png, application/pdf, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/msword, application/vnd.ms-excel" />
                        @break

                    @default
                        Tipo de datoxevento incorrecto: {{ $datoxevento['tipo'] }}
                        @break
                @endswitch
            </label>
        @endforeach

        @if(isset($tiene_descuentos) && $tiene_descuentos)
        <div class="form-group">
            <label for="codigo_descuento">Código de descuento</label>
            <input type="text" name="codigo_descuento" id="codigo_descuento" value="{{ isset($post['codigo_descuento']) ? $post['codigo_descuento'] : '' }}" />
            @if(isset($mensajes['descuento']))
                <div class="error">{{ $mensajes['descuento'] }}</div>
            @endif
        </div>
        @endif

        <label for="observacion">
            <span>Observación</span>
            <textarea name="observacion" placeholder="Puede escribir cualquier observación que crea necesaria...">{{ $post['observacion'] }}</textarea>
        </label>
    </section>

    <section id="botones">
        <input type="submit" name="boton" value="Inscribirme" id="inscribirme" onclick="return validar()" />
        <input type="submit" name="boton" value="Inscribiendo ..." id="inscribiendo" disabled="disabled" style="display: none; cursor: auto;" />
        <br style="clear:both;">
    </section>
</form>

@endsection