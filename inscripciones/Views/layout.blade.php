<!DOCTYPE html>
<html lang="es">

<head>
    <title>Inscripción {{ is_array($evento) && isset($evento['nombre']) ? $evento['nombre'] : '' }} - cronometrajeinstantaneo.com</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="stylesheet" type="text/css" href="{{ $url_inscripciones }}inscripciones.css?hash={{ $hash }}">
    <link rel="icon" href="{{ $url_inscripciones }}favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" href="{{ $url_inscripciones }}favicon.ico" type="image/x-icon"/>

    <style>
    {{ is_array($evento) && isset($evento['inscripciones_estilo']) ? $evento['inscripciones_estilo'] : '' }}
    </style>

    <script>
        @if (isset($categorias))
        var categorias = {!! json_encode($categorias) !!};
        const multi_categoria = {{ is_array($evento) && isset($evento['multi_categoria']) ? $evento['multi_categoria'] : 0 }};
        var categoriasSeleccionadas = [];
        var carreras_juntas = [];
        @else
        const multi_categoria = 0;
        @endif
    </script>
    <script src="{{ $url_inscripciones }}jquery-3.7.1.min.js"></script>
    <script src="{{ $url_inscripciones }}inscripciones.js?hash={{ $hash }}"></script>
    
    @if (isset($evento) && is_array($evento) && isset($evento['marketing']) && $evento['marketing'])
        <script src="{{ $url_inscripciones }}meta-pixel.js"></script>
    @endif

    <style type="text/css">
        @import url("https://fonts.googleapis.com/css?family=Open+Sans|Dosis|Roboto|Maven+Pro|Cairo|Archivo|Joti+One");
        @import url("https://fonts.googleapis.com/css2?family=Open+Sans&family=Teko:wght@300;600;700&&display=swap");
        @import url("https://fonts.googleapis.com/css2?family=Archivo+Narrow:ital,wght@1,700&family=Joti+One&family=Montserrat:wght@100;400;700&display=swap");
    </style>

</head>

<body>

    <header>
        <h1>Inscripción {{ is_array($evento) && isset($evento['nombre']) ? $evento['nombre'] : '' }}</h1>
    </header>

    @if (isset($mensajes) && is_array($mensajes) && count($mensajes))
        <div id="mensajes">
            @foreach ($mensajes as $mensaje)
                @php
                    $color = '';
                    if (is_array($mensaje) && isset($mensaje['tipo'])) {
                        switch ($mensaje['tipo']) {
                            case 'success':
                                $color = '#22c55e';
                                break;
                            case 'error':
                                $color = '#ef4444';
                                break;
                            case 'warning':
                                $color = '#eab308';
                                break;
                            case 'info':
                                $color = '#d1ecf1';
                                break;
                        }
                    }
                @endphp
                <div class="{{ is_array($mensaje) && isset($mensaje['tipo']) ? $mensaje['tipo'] : '' }}" style="background-color: {{ $color }}; border-left: 5px solid; margin-bottom: 10px; border-radius: 3px; padding: 10px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); transition: all 0.3s ease-in-out;">
                    {{ is_array($mensaje) && isset($mensaje['texto']) ? $mensaje['texto'] : '' }}
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="cerrar">✖️</span>
                </div>
            @endforeach
        </div>
    @endif

    <div class="container">
        @yield('content')
    </div>


    @if (is_array($evento) && isset($evento['inscripciones_js']) && $evento['inscripciones_js'])
        <script>{!! $evento['inscripciones_js'] !!}</script>
    @endif

    @if (isset($evento) && is_array($evento) && isset($evento['marketing']) && $evento['marketing'])
        @if (isset($marketing_pixels) && is_array($marketing_pixels))
            @foreach ($marketing_pixels as $pixel)
                {!! $pixel['code'] !!}
            @endforeach
        @endif
    @endif

    @if (isset($sponsors) && is_array($sponsors) && count($sponsors))
        <div class="sponsors">
            @foreach ($sponsors as $sponsor)
                {!! $sponsor !!}
            @endforeach
        </div>
    @endif

    <footer>
        <a href="https://cronometrajeinstantaneo.com" target="_blank"><img src="{{ $url_inscripciones }}isotipo_mini.png">&nbsp;<span class="firma">cronometrajeinstantaneo.com</span></a>
    </footer>

</body>
</html>