@extends('layout')

@section('content')

<div class="form">
    <section class="datos">

        @if ($inscripciones_preinscripto)
            {!! html_entity_decode($inscripciones_preinscripto) !!}
        @endif

        @if (count($precios))
        <hr>
            @foreach ($precios as $precio)
            <div class="precio">
                <div class="precio-logo">
                    <img src="{{ $url_inscripciones }}logo-{{ $precio['plataforma'] }}.png" />
                </div>
                @if ($precio['fecha_desde'] || $precio['fecha_hasta'] || $precio['cantidad'] || $precio['titulo'])
                <div class="precio-content">
                    @if ($precio['titulo'])
                    <h2>{{ $precio['titulo'] }}</h2>
                    @elseif ($precio['titulo_plataforma'])
                    <h2>{{ $precio['titulo_plataforma'] }}</h2>
                    @else
                    <h2>Pagar con {{ ucwords(str_replace('-', ' ', $precio['plataforma'])) }}</h2>
                    @endif
                    <p class="precio-message">
                        @if ($precio['fecha_desde'])
                            Desde: <span style="grey">{{ date('d/m/Y H:i', strtotime($precio['fecha_desde'])) }}</span><br>
                        @endif
                        @if ($precio['fecha_hasta'])
                            Hasta: <span style="grey">{{ date('d/m/Y H:i', strtotime($precio['fecha_hasta'])) }}</span><br>
                        @endif
                        @if ($precio['cantidad'])
                            Cupos total: <span style="grey">{{ $precio['cantidad'] }}</span><br>
                            Cupos inscriptos: <span style="grey">{{ $precio['cantidad_inscriptos'] }}</span><br>
                            Cupos disponibles: <span style="grey">{{ $precio['cantidad'] - $precio['cantidad_inscriptos'] }}</span>
                        @endif
                    </p>
                </div>
                @endif
                <div class="precio-amount">
                    @if (isset($precio['precio_original']) && isset($precio['descuento']))
                        <p class="precio-amount-text">
                            <span style="color: #888; text-decoration: line-through;">
                            Precio: $ {{ number_format($precio['precio_original'], 2, ',', '.') }}
                            </span><br>
                            Precio con descuento: $ {{ number_format($precio['precio'], 2, ',', '.') }}
                        </p>
                    @else
                        <p class="precio-amount-text">
                            Precio: $ {{ number_format($precio['precio'], 2, ',', '.') }}
                        </p>
                    @endif
                    @if ($precio['habilitado'])
                        @if ($precio['plataforma'] == 'boton-mercadopago' || $precio['plataforma'] == 'paypal')
                            <?php echo $precio['url']; ?>
                        @elseif ($precio['plataforma'] != 'efectivo')
                            <a href="{{ $precio['url'] }}" class="button">{{ $precio['boton'] ? $precio['boton'] : 'Pagar' }}</a>
                        @endif
                    @else
                        <input type="button" name="boton" value="Cerrado" class="button" disabled style="background-color: grey;"/>
                    @endif
                </div>
            </div>
            @endforeach
        @endif
    </section>
</div>

@endsection