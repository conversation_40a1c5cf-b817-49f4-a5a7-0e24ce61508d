@extends('layout')

@section('content')

<div class="form">
    <section class="datos">

        @if (isset($precio['descripcion']))
            <div class="precio-descripcion">{!! $precio['descripcion'] !!}</div>
        @endif

        @if ($precio['plataforma'] == 'mercadopago' && isset($pago->id) && $pago->id)

            <div id="wallet_container"></div>

            <script src="https://sdk.mercadopago.com/js/v2"></script>

            <script>
                const mp = new MercadoPago("{{ $precio['key'] }}");
                const bricksBuilder = mp.bricks();

                mp.bricks().create("wallet", "wallet_container", {
                    initialization: {
                        preferenceId: "{{ $pago->id }}"
                    }
                });
            </script>

        @elseif ($precio['plataforma'] == 'paypal')

        @endif

        @if(isset($precio['precio_original']) && isset($precio['descuento']))
            <div class="precio">
                <div class="precio-content" style="width: 100%; padding: 20px 30px;">
                    <div>
                        <div>Precio original: <b>${{ number_format($precio['precio_original'], 2, ',', '.') }}</b></div>
                        <div>Descuento: <b>{{ $precio['descuento'] }}%</b></div>
                        <div>Monto descontado: <b>${{ number_format($precio['precio_original'] - $precio['precio'], 2, ',', '.') }}</b></div>
                        <div>Precio con descuento: <b>${{ number_format($precio['precio'], 2, ',', '.') }}</b></div>
                    </div>
                </div>
            </div>
        @else
            <div class="precio">
                <div class="precio-content" style="width: 100%; padding: 20px 30px;">
                    <div>
                        <div>Precio: <b>${{ number_format($precio['precio'], 2, ',', '.') }}</b></div>
                    </div>
                </div>
            </div>
        @endif

        @if ($precio['plataforma'] == 'transferencia')
            <form class="form-transferencia" action="" method="POST" enctype="multipart/form-data">
                @if(isset($mensaje_error) && $mensaje_error)
                    <div class="form-group" style="color: #d9534f; font-weight: bold; text-align: center;">
                        {{ $mensaje_error }}
                    </div>
                @elseif(isset($mensaje_exito) && $mensaje_exito)
                    <div class="form-group" style="color: #5cb85c; font-weight: bold; text-align: center;">
                        {{ $mensaje_exito }}
                    </div>
                @else
                    <div class="form-group">
                        <label for="comprobante_pago">Subí el comprobante de la transferencia bancaria (PDF, JPG, PNG):</label>
                        <input type="file" name="comprobante_pago" id="comprobante_pago" accept=".pdf,.jpg,.jpeg,.png" required>
                    </div>
                    @if(isset($precio['cuotas']) && $precio['cuotas'] > 1)
                        @for($i = 2; $i <= min(6, intval($precio['cuotas'])); $i++)
                            <div class="form-group">
                                <label for="comprobante_pago{{ $i }}">Comprobante de cuota {{ $i }} (opcional):</label>
                                <input type="file" name="comprobante_pago{{ $i }}" id="comprobante_pago{{ $i }}" accept=".pdf,.jpg,.jpeg,.png">
                            </div>
                        @endfor
                    @endif
                    <div class="form-group">
                        <input type="submit" class="btn btn-primary" value="Enviar comprobante">
                    </div>
                @endif
            </form>
        @endif

    </section>
</div>

@endsection