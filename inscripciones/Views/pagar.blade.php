@extends('layout')

@section('content')

<div class="form">
    <section class="datos">

        @if (isset($precio['descripcion']))
            <div class="precio-descripcion">{!! $precio['descripcion'] !!}</div>
        @endif

        @if ($precio['plataforma'] == 'mercadopago' && isset($pago->id) && $pago->id)

            <div id="wallet_container"></div>

            <script src="https://sdk.mercadopago.com/js/v2"></script>

            <script>
                const mp = new MercadoPago("{{ $precio['key'] }}");
                const bricksBuilder = mp.bricks();

                mp.bricks().create("wallet", "wallet_container", {
                    initialization: {
                        preferenceId: "{{ $pago->id }}"
                    }
                });
            </script>

        @elseif ($precio['plataforma'] == 'paypal')

        @elseif ($precio['plataforma'] == 'transferencia-inteligente')
            <div class="transferencia-inteligente-info">
                @if(isset($pago) && !empty($pago))
                    <div class="form-group">
                        <h3>Datos para transferencia</h3>
                        <p>Realiza la transferencia a la siguiente cuenta generada automáticamente para tu inscripción:</p>

                        <div class="datos-cuenta" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;">
                            @if(isset($pago['account_number']))
                                <div style="margin-bottom: 10px;"><strong>Número de cuenta:</strong> <span style="font-family: monospace; font-size: 1.1em;">{{ $pago['account_number'] }}</span></div>
                            @endif

                            @if(isset($pago['bank_name']))
                                <div style="margin-bottom: 10px;"><strong>Banco:</strong> {{ $pago['bank_name'] }}</div>
                            @endif

                            @if(isset($pago['clabe']))
                                <div style="margin-bottom: 10px;"><strong>CLABE:</strong> <span style="font-family: monospace; font-size: 1.1em;">{{ $pago['clabe'] }}</span></div>
                            @endif

                            @if(isset($pago['cvu']))
                                <div style="margin-bottom: 10px;"><strong>CVU:</strong> <span style="font-family: monospace; font-size: 1.1em;">{{ $pago['cvu'] }}</span></div>
                            @endif

                            @if(isset($pago['account_holder']))
                                <div style="margin-bottom: 10px;"><strong>Titular:</strong> {{ $pago['account_holder'] }}</div>
                            @endif

                            @if(isset($pago['bank_code']))
                                <div style="margin-bottom: 10px;"><strong>Código de banco:</strong> {{ $pago['bank_code'] }}</div>
                            @endif
                        </div>

                        <div class="instrucciones" style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h4 style="margin-top: 0; color: #155724;">Instrucciones de pago:</h4>
                            <p><strong>Monto exacto a transferir:</strong> <span style="font-size: 1.2em; color: #155724;">${{ number_format($precio['precio'], 2, ',', '.') }}</span></p>
                            <ul style="margin: 10px 0;">
                                <li>Realiza la transferencia por el monto exacto indicado</li>
                                <li>Esta cuenta bancaria es única para tu inscripción</li>
                                <li>El pago será procesado automáticamente una vez acreditado</li>
                                <li>Recibirás una confirmación por email cuando se procese el pago</li>
                                <li>Conserva el comprobante de transferencia para tu registro</li>
                            </ul>
                        </div>

                        @if(isset($pago['customer_id']))
                            <div style="margin-top: 20px; padding: 10px; background: #f1f3f4; border-radius: 4px; font-size: 0.9em; color: #666;">
                                <strong>Referencia de inscripción:</strong> {{ $pago['customer_id'] }}
                            </div>
                        @endif
                    </div>
                @else
                    <div class="form-group">
                        <div style="color: #d9534f; font-weight: bold; text-align: center; padding: 20px; background: #f8d7da; border-radius: 8px;">
                            <h4>Error al generar los datos de transferencia</h4>
                            <p>No se pudieron obtener los datos bancarios automáticamente.</p>
                            <p>Por favor, intenta nuevamente o contacta al organizador del evento.</p>
                        </div>
                    </div>
                @endif
            </div>

        @endif

        @if(isset($precio['precio_original']) && isset($precio['descuento']))
            <div class="precio">
                <div class="precio-content" style="width: 100%; padding: 20px 30px;">
                    <div>
                        <div>Precio original: <b>${{ number_format($precio['precio_original'], 2, ',', '.') }}</b></div>
                        <div>Descuento: <b>{{ $precio['descuento'] }}%</b></div>
                        <div>Monto descontado: <b>${{ number_format($precio['precio_original'] - $precio['precio'], 2, ',', '.') }}</b></div>
                        <div>Precio con descuento: <b>${{ number_format($precio['precio'], 2, ',', '.') }}</b></div>
                    </div>
                </div>
            </div>
        @else
            <div class="precio">
                <div class="precio-content" style="width: 100%; padding: 20px 30px;">
                    <div>
                        <div>Precio: <b>${{ number_format($precio['precio'], 2, ',', '.') }}</b></div>
                    </div>
                </div>
            </div>
        @endif

        @if ($precio['plataforma'] == 'transferencia')
            <form class="form-transferencia" action="" method="POST" enctype="multipart/form-data">
                @if(isset($mensaje_error) && $mensaje_error)
                    <div class="form-group" style="color: #d9534f; font-weight: bold; text-align: center;">
                        {{ $mensaje_error }}
                    </div>
                @elseif(isset($mensaje_exito) && $mensaje_exito)
                    <div class="form-group" style="color: #5cb85c; font-weight: bold; text-align: center;">
                        {{ $mensaje_exito }}
                    </div>
                @else
                    <div class="form-group">
                        <label for="comprobante_pago">Subí el comprobante de la transferencia bancaria (PDF, JPG, PNG):</label>
                        <input type="file" name="comprobante_pago" id="comprobante_pago" accept=".pdf,.jpg,.jpeg,.png" required>
                    </div>
                    @if(isset($precio['cuotas']) && $precio['cuotas'] > 1)
                        @for($i = 2; $i <= min(6, intval($precio['cuotas'])); $i++)
                            <div class="form-group">
                                <label for="comprobante_pago{{ $i }}">Comprobante de cuota {{ $i }} (opcional):</label>
                                <input type="file" name="comprobante_pago{{ $i }}" id="comprobante_pago{{ $i }}" accept=".pdf,.jpg,.jpeg,.png">
                            </div>
                        @endfor
                    @endif
                    <div class="form-group">
                        <input type="submit" class="btn btn-primary" value="Enviar comprobante">
                    </div>
                @endif
            </form>
        @endif

    </section>
</div>

@endsection