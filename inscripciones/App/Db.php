<?php

namespace Inscripciones;
use PDO;

class Db
{
    private static $db = null;

    public static function db(): PDO
    {
        if (is_null(self::$db)) {
            self::$db = new PDO(
                'mysql:host='.BD_HOST.';dbname='.BD_BD.';charset=utf8mb4',
                BD_USER,
                BD_PASS,
                array(
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4")
            );
        }

        return self::$db;
    }

    public function __clone()
    {
        // Prevent cloning of the instance of the Singleton instance.
    }

    public function __wakeup()
    {
        // Prevent unserializing of the Singleton instance.
    }
}