<?php

namespace Inscripciones;

use PDO;

use Inscripciones\Db;
use Inscripciones\Route;

class Evento
{
    private $db;
    private $evento;
    private $route;
    private $datosxevento;
    private $categoria_sexo;
    private $idcategorias;
    private $categorias;
    private $carreras;
    public $idevento;

    public function __construct(Route $route)
    {
        $this->route = $route;
        $this->db = Db::db();
        $this->fetchEvento();
    }

    private function fetchEvento()
    {
        $stmt = $this->db->prepare(
            "SELECT eventos.*, config_organizacion.*,
            organizaciones.nombre AS nombre_organizacion, organizaciones.mail AS mail_organizacion,
                (SELECT idparticipante FROM participantes WHERE participantes.idevento = eventos.idevento AND participantes.estado != 'eliminado' ORDER BY idparticipante DESC LIMIT 1) AS ultimo_idparticipante,
                paises.timezone, paises.nombre_es AS pais
            FROM eventos
                JOIN organizaciones ON organizaciones.idorganizacion = eventos.idorganizacion
                JOIN config_organizacion ON config_organizacion.idevento = eventos.idevento
                LEFT JOIN paises ON eventos.idpais = paises.id
            WHERE eventos.codigo = :codigo AND eventos.estado != 'cancelado'
            LIMIT 1");

        $stmt->execute(['codigo' => $this->route->codigo()]);
        $evento = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$evento)
            return false;

        if (!$evento['mail'])
            $evento['mail'] = $evento['mail_organizacion'];

        $this->evento = $evento;

        foreach($evento as $key => $value) {
            $this->{$key} = $value;
        }

        $this->fetchDatosXEvento();
        $this->fetchCategorias();
        $this->fetchCarreras();
        $this->fetchNombresEtapas();
    }

    private function fetchDatosXEvento()
    {
        $stmt = $this->db->prepare(
                "SELECT datosxeventos.iddato, datosxeventos.obligatorio, datosxeventos.observacion,
                    datos.nombre, datos.tipo, datos.observacion AS datos_observacion
                FROM datosxeventos
                    LEFT JOIN datos ON datosxeventos.iddato=datos.iddato
                WHERE datosxeventos.idevento = :idevento
                    AND datosxeventos.inscripciones = 1
                ORDER BY datosxeventos.orden");

        $stmt->execute(['idevento' => $this->idevento]);
        $temp_datosxevento = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Después necesito el idcarrera como key, así que lo armo ahora
        $datosxevento = array();
        foreach ($temp_datosxevento as $datoxevento) {
            $datosxevento[$datoxevento['iddato']] = $datoxevento;
        }

        $this->datosxevento = $datosxevento;

    }

    private function fetchCategorias()
    {
        $sql_idcarrera = $this->route->parametro('idcarreras')
            ? $this->route->parametro('idcarreras')
            : "SELECT idcarrera FROM carreras WHERE idevento = :idevento";

        if ($this->route->parametro('idcarreras')) {
            $stmt = $this->db->prepare(
                "SELECT *
                FROM categorias
                WHERE idcarrera IN ($sql_idcarrera)
                    AND equipo = :equipo
                    AND inscripciones = 1
                ORDER BY orden, sexo, idcarrera, nombre");
            $stmt->execute(['equipo' => $this->route->equipo()]);
        } else {
            $stmt = $this->db->prepare(
                "SELECT *
                FROM categorias
                WHERE idcarrera IN ($sql_idcarrera)
                    AND equipo = :equipo
                    AND inscripciones = 1
                ORDER BY orden, sexo, idcarrera, nombre");
            $stmt->execute([
                'idevento' => $this->idevento,
                'equipo' => $this->route->equipo()
            ]);
        }

        $temp_categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!count($temp_categorias)) {
            $this->categorias = [];
            $this->categoria_sexo = [
                'masculino' => 0,
                'femenino' => 0,
                'otro' => 0,
                'mixto' => 0,
                'equipomasculino' => 0,
                'equipofemenino' => 0,
                'equipootro' => 0,
                'equipomixto' => 0,
            ];
            $this->idcategorias = "";

        } else {
            // Después necesito el idcategoria como key, así que lo armo ahora
            $categorias = array();
            $idcategorias_array = array();
            foreach ($temp_categorias as $categoria) {
                $categorias[$categoria['idcategoria']] = $categoria;
                $idcategorias_array[] = $categoria['idcategoria'];
            }
            $this->categorias = $categorias;

            $idcategorias = implode(',', $idcategorias_array);
            $this->idcategorias = $idcategorias;
            $this->categoria_sexo = $this->db
                ->query(
                    "SELECT
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'masculino') AS masculino,
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'femenino') AS femenino,
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'otro') AS otro,
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'mixto') AS mixto,
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'equipomasculino') AS equipomasculino,
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'equipofemenino') AS equipofemenino,
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'equipootro') AS equipootro,
                        (SELECT COUNT(*) FROM categorias
                            WHERE idcategoria IN ($idcategorias)
                            AND sexo = 'equipomixto') AS equipomixto
                    ")
                ->fetch(PDO::FETCH_ASSOC);
        }
    }

    public function fetchCarreras()
    {
        $params = [];

        if ($this->route->parametro('idcarrera')) {
            $sql_where = "idcarrera = :idcarrera";
            $params['idcarrera'] = $this->route->parametro('idcarrera');
        } else {
            $sql_where = "idevento = :idevento";
            $params['idevento'] = $this->idevento;
        }

        $sql_icategorias = $this->idcategorias
            ? " AND idcarrera IN (SELECT idcarrera FROM categorias WHERE idcategoria IN ($this->idcategorias))"
            : "";

        $stmt = $this->db->prepare(
            "SELECT idcarrera, nombre, inscripciones,
                inscripciones_texto, inscripciones_preinscripto, mail_preinscripto
            FROM carreras
            WHERE $sql_where $sql_icategorias
            ORDER BY orden, nombre");

        $stmt->execute($params);
        $temp_carreras = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Después necesito el idcarrera como key, así que lo armo ahora
        $carreras = array();
        foreach ($temp_carreras as $carrera) {
            $carreras[$carrera['idcarrera']] = $carrera;
        }

        $this->carreras = $carreras;
    }

    public function inscripcionesCerradas() : string
    {
        $fecha_inicio = $this->evento['fecha_inicio'];
        $fecha_fin = $this->evento['fecha_fin'];

        if (!$this->evento['abiertas']) {
            return "Las inscripciones están cerradas";

        // Parche límite de inscriptos en Tría de las Confluencias
        } else if ($this->idevento == 2301
            && $this->db->query("SELECT COUNT(*) FROM participantes WHERE idcategoria IN (SELECT idcategoria FROM categorias WHERE idcarrera = 11783)")->fetchColumn() >= 250) {
            return "Las inscripciones están cerradas porque no hay más cupos";

        } else if ($this->evento['terminada'] != '0000-00-00 00:00:00.000' || $this->evento['estado'] == 'terminado') {
            return "Las inscripciones se encuentran cerradas porque el evento ya terminó";

        } else if ($fecha_inicio && $fecha_fin) {
            $fecha_inicio_utc = strtotime($fecha_inicio) - $this->evento['timezone'] * 3600;
            $fecha_fin_utc = strtotime($fecha_fin) - $this->evento['timezone'] * 3600;

            if ($fecha_inicio_utc > time() && $this->evento['fecha_inicio'] != '0000-00-00 00:00:00') {
                return "Las inscripciones se abrirán el ".date('d/m/Y', strtotime($fecha_inicio)).' a las '.date('H:i', strtotime($fecha_inicio)).' hs.';
            } else if ($fecha_fin_utc < time() && $this->evento['fecha_fin'] != '0000-00-00 00:00:00') {
                return "Las inscripciones se cerrarón el ".date('d/m/Y', strtotime($fecha_fin)).' a las '.date('H:i', strtotime($fecha_fin)).' hs.';
            }
        }

        return false;
    }

    private function fetchNombresEtapas()
    {
        $stmt = $this->db->prepare(
            "SELECT orden, nombre, idetapa
            FROM etapas
            WHERE idevento = :idevento
            ORDER BY orden");

        $stmt->execute(['idevento' => $this->idevento]);
        $temp_etapas = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $etapas = array();
        foreach ($temp_etapas as $etapa) {
            $etapas[] = $etapa['nombre'];
        }

        $this->etapas = $etapas;
    }

    public function logos() : array
    {
        $url_archivos = 'https://storage.googleapis.com/cronometrajeinstantaneo-eventos/'.$this->idevento.'/';
        $extenciones = ['jpg', 'jpeg', 'png'];

        $logos = [];
        do {
            $i = count($logos) + 1;
            foreach ($extenciones as $extencion)
                if (checkRemoteFile($url_archivos.'logo_'.$i.'.'.$extencion))
                    $logos[] = '<img class="logo" src="'.$url_archivos.'logo_'.$i.'.'.$extencion.'">';
        } while (count($logos) >= $i and $i < 10);

        return $logos;
    }

    public function sponsors() : array
    {
        $url_archivos = 'https://storage.googleapis.com/cronometrajeinstantaneo-eventos/'.$this->idevento.'/';
        $extenciones = ['jpg', 'jpeg', 'png'];

        $sponsors = [];
        do {
            $i = count($sponsors) + 1;
            foreach ($extenciones as $extencion)
                if (checkRemoteFile($url_archivos.'sponsor_'.$i.'.'.$extencion))
                    $sponsors[] = '<img class="sponsor" src="'.$url_archivos.'sponsor_'.$i.'.'.$extencion.'">';
        } while (count($sponsors) >= $i and $i < 100);

        return $sponsors;
    }

    public function blade($variables = [])
    {
        return array_merge(
            [
                'evento' => $this->evento(),
                'url_inscripciones' => $this->route->url_inscripciones(),
                'url_resultados' => $this->route->url_resultados(),
                'hash' => file_get_contents(__DIR__.'/../../hash'),
                'logos' => $this->logos(),
                'sponsors' => $this->sponsors(),
            ],
            $variables
        );
    }

    public function evento() : Array
    {
        return $this->evento;
    }

    public function datosxevento() : Array
    {
        return $this->datosxevento;
    }

    public function carreras() : Array
    {
        return $this->carreras;
    }

    public function categorias() : Array
    {
        return $this->categorias;
    }

    public function categoria_sexo() : Array
    {
        return $this->categoria_sexo;
    }

    public function etapas() : Array
    {
        return $this->etapas;
    }

    public function tieneDescuentos()
    {
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM descuentos WHERE idevento = :idevento");
        $stmt->bindParam(':idevento', $this->idevento, PDO::PARAM_INT);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row && $row['total'] > 0;
    }

    public function marketingEnabled()
    {
        return isset($this->evento['marketing']) && $this->evento['marketing'];
    }

    public function marketingConfig()
    {
        return [
            'enabled' => $this->marketingEnabled()
        ];
    }

}