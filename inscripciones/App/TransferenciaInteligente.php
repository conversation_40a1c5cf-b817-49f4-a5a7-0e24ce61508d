<?php

namespace Inscripciones;

use PDO;

class TransferenciaInteligente
{
    private $db;
    private $precio;

    public function __construct($precio)
    {
        $this->precio = $precio;
        $this->db = Db::db();
    }

    public function authenticate()
    {
        // No necesita autenticación previa, se maneja en headers de cada request
        return true;
    }

    /**
     * Verifica si ya existe un pago generado para esta inscripción y precio
     */
    public function getExistingPayment($idevento, $idinscripcion, $idprecio)
    {
        $stmt = $this->db->prepare(
            "SELECT * FROM pagos
             WHERE idevento = :idevento
               AND idinscripcion = :idinscripcion
               AND idprecio = :idprecio
               AND referencia IS NOT NULL
               AND referencia != ''
             ORDER BY fecha DESC
             LIMIT 1"
        );
        $stmt->bindParam(':idevento', $idevento, PDO::PARAM_INT);
        $stmt->bindParam(':idinscripcion', $idinscripcion, PDO::PARAM_INT);
        $stmt->bindParam(':idprecio', $idprecio, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Crea una nueva cuenta en la API de Cucuru
     */
    public function createAccount($customer_id)
    {
        $url = 'https://api.cucuru.com/app/v1/Collection/accounts/account';

        $headers = [
            'Content-Type: application/json',
            'X-Cucuru-Api-Key: ' . $this->precio['secret'],
            'X-Cucuru-Collector-id: ' . $this->precio['key']
        ];

        $data = [
            'customer_id' => (string)$customer_id,
            'read_only' => 'false'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('Error de conexión con Cucuru: ' . $error);
        }

        if ($httpCode !== 200 && $httpCode !== 201) {
            throw new \Exception('Error en API de Cucuru. Código: ' . $httpCode . '. Respuesta: ' . $response);
        }

        $responseData = json_decode($response, true);

        if (!$responseData) {
            throw new \Exception('Respuesta inválida de la API de Cucuru');
        }

        return $responseData;
    }

    /**
     * Procesa la respuesta de la API y la formatea para uso interno
     */
    public function processApiResponse($apiResponse)
    {
        // Agregar información adicional para el procesamiento interno
        $processedData = $apiResponse;
        $processedData['created_at'] = date('Y-m-d H:i:s');
        $processedData['platform'] = 'transferencia-inteligente';

        return $processedData;
    }

    /**
     * Valida que la respuesta de la API contenga los datos necesarios
     */
    public function validateApiResponse($response)
    {
        if (!is_array($response)) {
            return false;
        }

        // Validar que tenga al menos un identificador de cuenta
        return isset($response['account_number']) ||
               isset($response['clabe']) ||
               isset($response['cvu']) ||
               isset($response['account_id']);
    }

    /**
     * Extrae el número de cuenta de la respuesta para usar como referencia
     */
    public function extractAccountReference($response)
    {
        if (isset($response['account_number'])) {
            return $response['account_number'];
        }

        if (isset($response['clabe'])) {
            return $response['clabe'];
        }

        if (isset($response['cvu'])) {
            return $response['cvu'];
        }

        if (isset($response['account_id'])) {
            return $response['account_id'];
        }

        // Fallback al customer_id si no hay otros identificadores
        return $response['customer_id'] ?? null;
    }
}
