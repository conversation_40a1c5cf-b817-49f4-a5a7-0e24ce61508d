<?php

namespace Inscripciones;

use MercadoPago\MercadoPagoConfig;
use MercadoPago\Client\Preference\PreferenceClient;
use MercadoPago\Exceptions\MPApiException;

class MercadoPago
{
    private $db;
    private $precio;

    public function __construct($precio)
    {
        $this->precio = $precio;
        $this->db = Db::db();
    }

    public function authenticate()
    {
        // Set the token the SDK's config
        $mpAccessToken = $this->precio['secret'];
        MercadoPagoConfig::setAccessToken($mpAccessToken);

        // (Optional) Set the runtime enviroment to LOCAL if you want to test on localhost
        // Default value is set to SERVER
        // MercadoPagoConfig::setRuntimeEnviroment(MercadoPagoConfig::LOCAL);
    }

    // Function that will return a request object to be sent to Mercado Pago API
    public function createPreferenceRequest($parametros)
    {
        try {
            $client = new PreferenceClient();
            $preference = $client->create($parametros);
            return $preference;
        } catch (MPApiException $e) {
            echo "Status code: " . $e->getApiResponse()->getStatusCode() . "\n";
            echo "Content: ";
            var_dump($e->getApiResponse()->getContent());
            echo "\n";
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
        return false;
    }

    public function recibir()
    {
        $args = [
            'payment_id' => FILTER_SANITIZE_NUMBER_INT,
            'status' => FILTER_SANITIZE_SPECIAL_CHARS,
            'external_reference' => FILTER_SANITIZE_NUMBER_INT,

            'merchant_order_id' => FILTER_SANITIZE_NUMBER_INT,
            'collection_id' => FILTER_SANITIZE_NUMBER_INT,
            'collection_status' => FILTER_SANITIZE_SPECIAL_CHARS,
            'payment_type' => FILTER_SANITIZE_SPECIAL_CHARS,
            'preference_id' => FILTER_SANITIZE_SPECIAL_CHARS,
            'site_id' => FILTER_SANITIZE_SPECIAL_CHARS,
            'processing_mode' => FILTER_SANITIZE_SPECIAL_CHARS,
            'merchant_account_id' => FILTER_SANITIZE_SPECIAL_CHARS,
        ];
        $get = filter_input_array(INPUT_GET, $args);

        switch ($get['status']) {
            case 'approved': $get['estado'] = 'aprobado'; break;
            case 'pending': $get['estado'] = 'pendiente'; break;
            case 'failure': $get['estado'] = 'rechazado'; break;
            default: $get['estado'] = ''; break;
        }

        $respuesta = [
            'referencia' => $get['payment_id'],
            'estado' => $get['estado'],
            'fecha' => date('Y-m-d H:i:s'),
            'json' => json_encode($get),
            'observaciones' => NULL,
        ];

        return $respuesta;
    }

}
