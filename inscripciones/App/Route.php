<?php

namespace Inscripciones;

use Inscripciones\Evento;

class Route
{
    private string $uri;
    private string $method;
    private string $codigo = '';
    private string $equipo;
    private int $idinscripcion = 0;
    private string $accion = '';
    private array $parametros = [];
    private array $post = [];

    private $url_inscripciones;
    private $url_resultados;
    const ACCIONES = ['preinscripto', 'pagar', 'inscripto', 'rechazado', 'pendiente', 'aprobar_ficha', 'aprobar_pago'];

    public function __construct()
    {
        // Habilito dominio alternativo
        $this->url_inscripciones = strpos(DOMINIO_ALTERNATIVO, $_SERVER['HTTP_HOST'])
            ? str_replace(DOMINIO, DOMINIO_ALTERNATIVO, URL_INSCRIPCIONES)
            : URL_INSCRIPCIONES;
        $this->url_resultados = strpos(DOMINIO_ALTERNATIVO, $_SERVER['HTTP_HOST'])
            ? str_replace(DOMINIO, DOMINIO_ALTERNATIVO, URL_RESULTADOS)
            : URL_RESULTADOS;

        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->uri =$_SERVER['REQUEST_URI'];

        $this->parseUri();
        $this->sanitizeParametros();

        // Log de la visita
        $this->logVisita();

        return;
    }

    private function parseUri()
    {
        $uri = explode('?', $this->uri)[0];
        $uri = explode('/', $uri);
        if ($uri[1] != 'inscripciones') {
            return;
        }

        $this->codigo = isset($uri[2]) ? $uri[2] : false;
        $this->equipo = isset($uri[3]) && in_array($uri[3], ['dupla', 'tria', 'tetra', 'equipo', 'posta'])
            ? (in_array($uri[3], ['equipo', 'posta']) ? 'tria' : $uri[3])
            : '';

        if (!$this->equipo && isset($uri[3]) && $uri[3])
            $encrypted = $uri[3];
        else if ($this->equipo && isset($uri[4]) && $uri[4])
            $encrypted = $uri[4];
        else
            return true;

        $method = 'AES-128-CBC';;
        $decrypted = openssl_decrypt(base64_decode(urldecode($encrypted)), $method, HASH_SALT);
        $decrypted_exploded = explode('&', $decrypted); // Debe tener la forma idinscripcion=1&accion=editar

        // Controlo si tiene los dos parámetros, si el idinscripcion es un número y si la acción es una de las permitidas
        if (count($decrypted_exploded) == 2) {
            $idinscripcion = explode('=', $decrypted_exploded[0]);
            $accion = explode('=', $decrypted_exploded[1]);

            if ($idinscripcion[0] == 'idinscripcion' && is_numeric($idinscripcion[1])
                && $accion[0] == 'accion' && in_array($accion[1], self::ACCIONES)) {
                $this->idinscripcion = $idinscripcion[1];
                $this->accion = $accion[1];

                return true;
            }
        }

        $this->errorUrl('El enlace es incorrecto o la inscripción ya no existe');
    }

    private function sanitizeParametros()
    {
        $parametros_aceptados = [
            'idcarreras' => FILTER_SANITIZE_SPECIAL_CHARS,
            'idprecio' => FILTER_SANITIZE_NUMBER_INT,
        ];

        foreach (explode('&', $_SERVER['QUERY_STRING']) as $query) {
            $parametro = explode('=', $query);

            if (array_key_exists($parametro[0], $parametros_aceptados) && isset($parametro[1])) {
                $this->parametros[$parametro[0]] = filter_input(INPUT_GET, $parametro[0], $parametros_aceptados[$parametro[0]]);
            }
        }

        if (isset($this->parametros['idcarreras'])) {
            $this->parametros['idcarreras'] = filtrar_input_array_coma($this->parametros['idcarreras']);
        }
    }

    public function sanitizePost(Evento $evento)
    {
        // Para los eventos que tienen equipos en distintas modalidades, utilizo en $i = 0 para el nombre del equipo y después el $i para cada participante
        $participantesxequipo = $this->participantesxequipo();

        $args = array(
            'boton' => FILTER_SANITIZE_SPECIAL_CHARS,
            'nombre' => FILTER_SANITIZE_SPECIAL_CHARS,
            'sexo' => FILTER_SANITIZE_SPECIAL_CHARS,
            'idcarrera' => FILTER_VALIDATE_INT,
            'idcategoria' => FILTER_VALIDATE_INT,
            'idcategorias' => FILTER_SANITIZE_SPECIAL_CHARS,
            'codigo_descuento' => FILTER_SANITIZE_SPECIAL_CHARS,
        );

        for ($i = 1; $i <= $participantesxequipo; $i++) {
            $agregado = $participantesxequipo == 1 ? '' : $i;

            $args['nombre'.$agregado] = FILTER_SANITIZE_SPECIAL_CHARS;
            $args['apellido'.$agregado] = FILTER_SANITIZE_SPECIAL_CHARS;
            $args['mail'.$agregado] = FILTER_SANITIZE_EMAIL;
            $args['localidad'.$agregado] = FILTER_SANITIZE_SPECIAL_CHARS;
            $args['observacion'.$agregado] = FILTER_SANITIZE_SPECIAL_CHARS;

            foreach ($evento->datosxevento() as $datoxevento) {
                switch ($datoxevento['tipo']) {
                    case 'text':
                    case 'date':
                    case 'select':
                    // case 'file':
                    case 'acepta':
                        $args[$datoxevento['iddato'].$agregado] = FILTER_SANITIZE_SPECIAL_CHARS;
                        break;

                    case 'number':
                        $args[$datoxevento['iddato'].$agregado] = FILTER_SANITIZE_NUMBER_INT;
                        break;

                    case 'pregunta':
                        $args[$datoxevento['iddato'].$agregado] = FILTER_SANITIZE_SPECIAL_CHARS;
                        $args[$datoxevento['iddato'].$agregado.'_texto'] = FILTER_SANITIZE_SPECIAL_CHARS;
                        break;
                }

            // Agregar tracking de parámetros UTM (solo los principales)
            $utm_params = ['utm_source', 'utm_medium', 'utm_campaign'];
            foreach ($utm_params as $utm_param) {
                if (isset($_GET[$utm_param]) || isset($_POST[$utm_param])) {
                    $args[$utm_param] = FILTER_SANITIZE_SPECIAL_CHARS;
                }
            }
            }
        }

        $post = filter_input_array(INPUT_POST, $args, true);
        $post ??= []; // Si no hay POST, se crea un array vacío

        // Convertir todos los valores del array a UTF-8
        $post = array_map(function($item) {
            return mb_convert_encoding($item, 'UTF-8', mb_detect_encoding($item));
        }, $post);

        foreach ($args as $key => $arg) {
            $post[$key] = (isset($post[$key]) && !is_null($post[$key])) ? trim($post[$key]) : '';
        }
        $post['nombre'] = isset($post['nombre']) ? html_entity_decode($post['nombre'], ENT_QUOTES, 'UTF-8') : '';
        $post['apellido'] = isset($post['apellido']) ? html_entity_decode($post['apellido'], ENT_QUOTES, 'UTF-8') : '';
        $post['mail'] = $post['mail'] ?? '';
        $post['sexo'] = $post['sexo'] ?? '';
        $post['idcategoria'] = $post['idcategoria'] ?? '';
        $post['localidad'] = isset($post['localidad']) ? html_entity_decode($post['localidad'], ENT_QUOTES, 'UTF-8') : '';
        $post['observacion'] = isset($post['observacion']) ? html_entity_decode($post['observacion'], ENT_QUOTES, 'UTF-8') : '';

        $this->post = $post;
    }

    public function participantesxequipo()
    {
        switch ($this->equipo) {
            default:
            case '': return 1; break;
            case 'dupla': return 2; break;
            case 'tria': return 3; break;
            case 'tetra': return 4; break;
        }
    }

    public function errorUrl($mensaje = '')
    {
        header("HTTP/1.1 400 Bad Request");
        // TODO: Hacer un lindo mensaje y redirección a la lista de eventos
        exit($mensaje);
    }

    public function generateUri($idinscripcion, $accion)
    {
        $data = 'idinscripcion='.$idinscripcion.'&accion='.$accion;
        $method = 'AES-128-CBC';

        $encrypted = @openssl_encrypt($data, $method, HASH_SALT);

        return $this->url_inscripciones . $this->codigo . '/'
            . ($this->equipo ? $this->equipo . '/' : '')
            . urlencode(base64_encode($encrypted));
    }

    public function redirect($url)
    {
        header('Location: '.$this->generateUri($this->idinscripcion(), $url));
        exit;
    }

    public function uri()
    {
        return $this->uri;
    }

    public function method()
    {
        return $this->method;
    }

    public function codigo()
    {
        return $this->codigo;
    }

    public function idinscripcion($idinscripcion = false)
    {
        if ($idinscripcion)
            $this->idinscripcion = $idinscripcion;

        return $this->idinscripcion;
    }

    public function equipo()
    {
        return $this->equipo;
    }

    public function accion()
    {
        return $this->accion;
    }

    public function parametro($parametro)
    {
        if (!isset($this->parametros[$parametro]))
            return false;

        return $this->parametros[$parametro];
    }

    public function post($parametro = false)
    {
        if (!$parametro)
            return $this->post;

        if (!isset($this->post[$parametro]))
            return false;

        return $this->post[$parametro];
    }

    public function url_inscripciones()
    {
        return $this->url_inscripciones;
    }

    public function url_resultados()
    {
        return $this->url_resultados;
    }

    /**
     * Log de todas las visitas al sistema
     */
    private function logVisita()
    {
        $logger = new Logger();

        $datos = [
            $this->codigo ?: 'N/A',
            $this->equipo ?: 'N/A',
            $this->idinscripcion ?: 'N/A',
            $this->accion ?: 'N/A',
            $this->method,
            $_SERVER['REQUEST_URI'] ?? 'N/A',
            $_SERVER['HTTP_REFERER'] ?? 'N/A'
        ];

        $logger->log('VISITA', $datos, 'visitas');
    }
}