<?php

require __DIR__.'/../../cronometrajeinstantaneo.env';
require __DIR__.'/../../funciones.php';
require __DIR__.'/../../vendor/autoload.php';

use Inscripciones\Route;
use Inscripciones\Evento;
use Inscripciones\Inscripcion;
use Inscripciones\Pago;

use eftec\bladeone\BladeOne;

if (ESTADO == 'produccion')
    \Sentry\init([
        'dsn' => SENTRY_DNS,
        'traces_sample_rate' => SENTRY_TRACES_SAMPLE_RATE,
        'profiles_sample_rate' => SENTRY_PROFILES_SAMPLE_RATE,
    ]);


$route = new Route();
if (!$route->codigo())
    $route->errorUrl('Enlace incorrecto');

$evento = new Evento($route);
if (is_null($evento->idevento))
    $route->errorUrl('No existe el evento');

if ($route->method() == 'POST') {
    $route->sanitizePost($evento);
}

// Inicializar tracking de marketing solo si está habilitado
if ($evento->marketingEnabled()) {
    $tracking = new MarketingTracking($evento->idevento);
    $pixels = new MarketingPixels($evento->idevento);
}


$blade = new BladeOne(
    __DIR__ . '/../Views',
    __DIR__ . '/../cache',
    ESTADO == 'desarrollo' ? BladeOne::MODE_DEBUG : BladeOne::MODE_AUTO);

// Inscripciones cerradas
if ($evento->inscripcionesCerradas()) {
    echo $blade->run("cerradas", $evento->blade([
        'razon' => $evento->inscripcionesCerradas(),
    ]));

// Nueva inscripción
} else if ($route->method() == 'GET' && !$route->idinscripcion()) {

    // Tracking de visita al formulario
    if ($evento->marketingEnabled()) {
        $tracking->trackVisit($evento->idevento, 'event_visit');

        $visit_pixels = $pixels->getPixelsForStep('event_visit');
        $pixel_codes = [];

        foreach ($visit_pixels as $pixel) {
            $pixel_codes[] = [
                'code' => $pixels->generatePixelCode($pixel, 'event_visit')
            ];
        }

        $evento->blade_data['marketing_pixels'] = $pixel_codes;
    }

    echo $blade->run(
        $route->participantesxequipo() == 1 ? 'individual' : 'equipo',
        $evento->blade([
            'categorias' => $evento->categorias(),
            'categoria_sexo' => $evento->categoria_sexo(),
            'carreras' => $evento->carreras(),
            'datosxeventos' => $evento->datosxevento(),
            'post' => [],
            'iddato_unico' => false,
            'participantesxequipo' => $route->participantesxequipo(),
            'etapas' => $evento->etapas(),
            'tiene_descuentos' => $evento->tieneDescuentos(),
        ]));

// Recibo nueva inscripción por post
} else if ($route->method() == 'POST' && !$route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);
    if ($inscripcion->save()) {

        // Tracking de formulario completado
        if ($evento->marketingEnabled()) {
            $tracking->trackVisit($evento->idevento, 'form_complete', $inscripcion->idinscripcion());

            $form_pixels = $pixels->getPixelsForStep('form_complete');
            $pixel_codes = [];

            foreach ($form_pixels as $pixel) {
                $pixel_codes[] = [
                    'code' => $pixels->generatePixelCode($pixel, 'form_complete')
                ];
            }

            $evento->blade_data['marketing_pixels'] = $pixel_codes;
        }

        cache($route->codigo());
        $inscripcion->notificar('preinscripto');
        $route->idinscripcion($inscripcion->idinscripcion());
        $route->redirect('preinscripto');

    } else {
        echo $blade->run(
            $route->participantesxequipo() == 1 ? 'individual' : 'equipo',
            $evento->blade([
                'categorias' => $evento->categorias(),
                'categoria_sexo' => $evento->categoria_sexo(),
                'carreras' => $evento->carreras(),
                'datosxeventos' => $evento->datosxevento(),
                'post' => $route->post(),
                'iddato_unico' => false,
                'participantesxequipo' => $route->participantesxequipo(),
                'etapas' => $evento->etapas(),

                'mensajes' => $inscripcion->mensajes(),
                'tiene_descuentos' => $evento->tieneDescuentos(),
            ]));
    }

// Ver inscripción por GET
} else if ($route->method() == 'GET'
    && $route->accion() == 'preinscripto'
    && $route->idinscripcion()) {

        $inscripcion = new Inscripcion($evento, $route);

        if (!$inscripcion->get())
            $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');

        if (in_array($inscripcion->estado, ['inscripto', 'acreditado']))
            $route->redirect('inscripto');

        // Sino cargar los precios
        $precios = $inscripcion->precios();

        echo $blade->run($route->accion(), $evento->blade([
            'inscripciones_preinscripto' => $inscripcion->prepararTextos($evento->evento()['inscripciones_preinscripto']),
            'precios' => $precios ?? [],
        ]));

// Pagar inscripción por GET
} else if ($route->method() == 'GET'
    && $route->accion() == 'pagar'
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);
    $inscripcion->get();
    $precios = $inscripcion->precios();

    if (!$inscripcion->validarPrecio($route->parametro('idprecio')))
        $route->errorUrl('El precio no está disponible en este momento');

    $precio = $precios[$route->parametro('idprecio')];

    // Validar si ya existe comprobante de pago cargado SOLO si es transferencia
    $mensaje_exito = null;
    if ($precio['plataforma'] == 'transferencia') {
        $datos_extra = $inscripcion->getDatosxParticipantes($route->idinscripcion());
        foreach ($datos_extra as $dato) {
            if ($dato['iddato'] === 'pago' && !empty($dato['dato'])) {
                $mensaje_exito = '¡Ya enviaste tu comprobante! El pago está pendiente de revisión por el organizador.';
                break;
            }
        }
    }

    $pago = new Pago($precio);
    $pago->setParametros([
        'idevento' => $evento->idevento,
        'idinscripcion' => $route->idinscripcion(),
        'iddescuento' => isset($inscripcion->inscripcion()['iddescuento']) ? $inscripcion->inscripcion()['iddescuento'] : null,
        'inscripto' => $route->generateUri($route->idinscripcion(), 'inscripto').'?idprecio='.$route->parametro('idprecio'),
        'rechazado' => $route->generateUri($route->idinscripcion(), 'rechazado').'?idprecio='.$route->parametro('idprecio'),
    ]);
    if ($precio['fecha_hasta'] && $precio['fecha_hasta'] > date('Y-m-d H:i:s')) {
        $pago->setParametros([
            'fecha_hasta' => $precio['fecha_hasta'],
        ]);
    }
    $pago->get();

    echo $blade->run($route->accion(), $evento->blade([
        'precio' => $precio,
        'pago' => $pago->pago(),
        'mensaje_exito' => $mensaje_exito,
    ]));

} elseif ($route->method() == 'POST'
    && $route->accion() == 'pagar'
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);
    $inscripcion->get();
    $precios = $inscripcion->precios();

    if (!$inscripcion->validarPrecio($route->parametro('idprecio')))
        $route->errorUrl('El precio no está disponible en este momento');

    $precio = $precios[$route->parametro('idprecio')];

    // Procesar archivos subidos
    $errores = [];
    $archivos = ['pago' => 'comprobante_pago'];
    if (isset($precio['cuotas']) && $precio['cuotas'] > 1) {
        for ($i = 2; $i <= min(6, intval($precio['cuotas'])); $i++) {
            $archivos['pago'.$i] = 'comprobante_pago'.$i;
        }
    }
    $links = [];
    foreach ($archivos as $iddato => $input) {
        if (isset($_FILES[$input]) && $_FILES[$input]['error'] === UPLOAD_ERR_OK && $_FILES[$input]['size'] > 0) {
            // Guardar archivo como dato extra y obtener el nombre
            $nombre_archivo = $inscripcion->guardarComprobantePago($iddato, $_FILES[$input]);
            if ($nombre_archivo) {
                $links[] = '<a style="display:inline-block;padding:10px 18px;background:#ff770d;color:#fff;text-decoration:none;border-radius:5px;margin:5px 0;" href="https://storage.googleapis.com/cronometrajeinstantaneo-datos/'.$nombre_archivo.'" target="_blank">Ver comprobante '.ucfirst($iddato).'</a>';
            }
        }
    }
    // Log temporal para depuración de links de comprobante
    file_put_contents(PATH_LOGS.'/debug_comprobante_links.log', date('Y-m-d H:i:s')."\n".print_r($links, true)."\n\n", FILE_APPEND);
    // Notificar al organizador y participante
    $notificaciones = new \Inscripciones\Notificaciones();
    $notificaciones->notificar('comprobante_pago', $inscripcion, [
        'links' => $links,
        'mensaje' => 'Se recibió un nuevo comprobante de pago.'
    ]);

    // Mostrar mensaje de éxito
    echo $blade->run('pagar', $evento->blade([
        'precio' => $precio,
        'pago' => null,
        'mensaje_exito' => '¡Comprobante enviado correctamente! El organizador revisará tu pago.',
    ]));

// Ver y procesar inscripto y rechazado
} else if ($route->method() == 'GET'
    && ($route->accion() == 'rechazado' || $route->accion() == 'pendiente')
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);

    if (!$inscripcion->get())
        $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');

    // TODO: Guardar los datos del pago rechazado por MP

    echo $blade->run($route->accion(), $evento->blade([
        'inscripciones_rechazado' => $inscripcion->prepararTextos($evento->evento()['inscripciones_rechazado']),
    ]));

// Recibo un pago aprobado para pasarlo a inscripto
} else if ($route->method() == 'GET'
    && $route->accion() == 'inscripto'
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);

    if (!$inscripcion->get())
        $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');

    if ($inscripcion->estado == 'preinscripto') {

        $precios = $inscripcion->precios();

        if (!$inscripcion->validarPrecio($route->parametro('idprecio')))
            $route->errorUrl('El precio no está disponible en este momento');

        $precio = $precios[$route->parametro('idprecio')];
        $pago = new Pago($precio);
        $pago->setParametros([
            'idevento' => $evento->idevento,
            'idinscripcion' => $route->idinscripcion(),
        ]);
        $recibido = $pago->recibir();

        if (!$recibido)
            $route->redirect('rechazado');

        $pago->save();

        // Tracking de pago completado
        if ($evento->marketingEnabled()) {
            $tracking->trackVisit($evento->idevento, 'payment_complete', $route->idinscripcion());

            $payment_pixels = $pixels->getPixelsForStep('payment_complete');
            $pixel_codes = [];

            foreach ($payment_pixels as $pixel) {
                $pixel_codes[] = [
                    'code' => $pixels->generatePixelCode($pixel, 'payment_complete', [
                        'value' => $precio['precio']
                    ])
                ];
            }

            $evento->blade_data['marketing_pixels'] = $pixel_codes;
        }

        $update = [
            'estado' => 'inscripto',
            'estado_pago' => $precio['cuotas'] > 1 ? 'parcial' : 'aprobado',
            'fechapago' => 'NOW()',
        ];

        if ($evento->auto_numeracion && !$inscripcion->idparticipante)
            $update['idparticipante'] = $inscripcion->siguienteIdparticipante();

        // Actualizo la inscripcion
        $inscripcion->update($update);

        // Mandar mails si corresponde
        if ($evento->auto_mail && $evento->mail_inscripto)
            $inscripcion->notificar('inscripto');

    }
    // TODO: Si ya estaba aprobado el pago, mostrar hemos registrado su pago el día ...

    echo $blade->run($route->accion(), $evento->blade([
        'inscripciones_'.$route->accion() => $inscripcion->prepararTextos($evento->evento()['inscripciones_'.$route->accion()]),
    ]));

// Aprobar ficha desde mail del organizador
} else if ($route->method() == 'GET'
    && $route->accion() == 'aprobar_ficha'
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);
    $logger = new \Inscripciones\Logger();

    if (!$inscripcion->get()) {
        $logger->log('ACCESO_ENLACE', [
            $evento->idevento,
            $route->idinscripcion(),
            $evento->codigo,
            'N/A',
            'aprobar_ficha',
            'INVALIDO'
        ], 'accesos');
        $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');
    }

    $logger->log('ACCESO_ENLACE', [
        $evento->idevento,
        $route->idinscripcion(),
        $evento->codigo,
        $inscripcion->nombre,
        'aprobar_ficha',
        'VALIDO'
    ], 'accesos');

    $aprobacion = new \Inscripciones\Aprobacion($inscripcion);
    $resultado = $aprobacion->aprobarFicha();

    if ($resultado) {
        $estados = $aprobacion->obtenerEstados();
        $mensaje = 'Ficha aprobada correctamente.';

        if ($estados['estado'] == 'inscripto') {
            $mensaje .= ' ¡La inscripción ha sido confirmada automáticamente!';
        }

        echo $blade->run('aprobacion_exitosa', $evento->blade([
            'mensaje' => $mensaje,
            'estados' => $estados,
            'inscripcion' => $inscripcion,
        ]));
    } else {
        $route->errorUrl('Error al aprobar la ficha');
    }

// Aprobar pago desde mail del organizador
} else if ($route->method() == 'GET'
    && $route->accion() == 'aprobar_pago'
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);
    $logger = new \Inscripciones\Logger();

    if (!$inscripcion->get()) {
        $logger->log('ACCESO_ENLACE', [
            $evento->idevento,
            $route->idinscripcion(),
            $evento->codigo,
            'N/A',
            'aprobar_pago',
            'INVALIDO'
        ], 'accesos');
        $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');
    }

    $logger->log('ACCESO_ENLACE', [
        $evento->idevento,
        $route->idinscripcion(),
        $evento->codigo,
        $inscripcion->nombre,
        'aprobar_pago',
        'VALIDO'
    ], 'accesos');

    $aprobacion = new \Inscripciones\Aprobacion($inscripcion);
    $resultado = $aprobacion->aprobarPago();

    if ($resultado) {
        $estados = $aprobacion->obtenerEstados();
        $mensaje = 'Pago aprobado correctamente.';

        if ($estados['estado'] == 'inscripto') {
            $mensaje .= ' ¡La inscripción ha sido confirmada automáticamente!';
        }

        echo $blade->run('aprobacion_exitosa', $evento->blade([
            'mensaje' => $mensaje,
            'estados' => $estados,
            'inscripcion' => $inscripcion,
        ]));
    } else {
        var_dump($resultado);
        exit('Error al aprobar el pago');
        $route->errorUrl('Error al aprobar el pago');
    }

} else {
    $route->errorUrl('Enlace incorrecto');
}
