# MUNDIAL


## TODO


---
## DOCUMENTACION

## ESTADÍSTICAS DE JUGADORES
SELECT COUNT(*) AS cantidad, localidad FROM `users` GROUP BY localidad ORDER BY cantidad DESC LIMIT 500;
SELECT COUNT(*) AS cantidad, (SELECT name FROM groups WHERE groups.id = group_user.group_id) AS nombre FROM `group_user` GROUP BY group_id ORDER BY cantidad DESC LIMIT 500;

### COMO ACTIVAR USUARIO DEV

- En .env poner APP_DEBUG=true
- En la carpeta de vue `npm run dev`
- Acceder a http://localhost:8081

### COMO DEPLOYAR

www
cd somoshinchada/laravel
git push prod
cd ../vue
git push prod
prod_crono

cd /var/www/somoshinchada/laravel
./deploy.sh


### COMO CREAR OTRO JUEGO EN OTRO DOMINIO

- [x] Comprar dominio y delegar
- [x] Configurar en Google DNS
  - elmundialenpimampiro
  - elmundialdebigsix
- [x] Configurar en Apache y probar con un info.php
sudo letsencrypt certonly --apache -d elmundialenpimampiro.com -d www.elmundialenpimampiro.com
sudo letsencrypt certonly --apache -d elmundialdebigsix.com.ar -d www.elmundialdebigsix.com.ar
- [x] Crear bases de datos
  - elmundialdebigsix ard/9SK(9ixh)RT.
  - elmundialenpimampiro u1z6TqFozRa(FpBg
- [x] Configurar gits y probar un deploy
- [x] Clonar ramas, cambiar enlaces e imagenes
- [x] Configurar .env, composer install, art migrate, npm install
cd storage
mkdir logs
mkdir framework
mkdir framework/cache && framework/cache/data
mkdir framework/sessions
mkdir framework/testing
mkdir framework/views
chgrp -R www-data ../storage
chown -R www-data ../storage

- [x] Configurar apps en Google y Facebook
- [x] Deploy y test final
