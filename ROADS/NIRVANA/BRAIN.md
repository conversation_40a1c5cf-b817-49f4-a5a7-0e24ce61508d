# 🧠 ROAD > BRAIN

Brain es mi segundo cerebro, sólo para uso y consulta personal.

- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor manualmente con `rup` y `rdown` y la funcion memory_hack
- Tengo herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI
- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar
- Hago backup en MEGA de todo por las dudas

- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.


# NUEVO TODO

Estoy jugando a reformular mis sistema TODO y BRAIN con lo siguiente en mente:

- Sólo texto markdown y todo en múltiples archivos (mi lugar es el teclado)
- Integrado a VSC y Sincronizado con celular (a través de MEGA y con markdown en celu también)
- No más Week, ni deadlines, todos flujos continuos @flow para hacer cuando quiera (estoy orgulloso de poder trabajar sólo cuando tengo ganas y de lo que tengo ganas)
- Planear cada @flow al iniciar con AI 10-80-10


## MEMORY

- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal
- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)


## PROCESOS Y FRAMEWORKS

- Puedo pasar audio a texto con MEGA y `transcribir`


## TODO

- [x] Crear el framework para [[next.md]]
- [x] Buscar forma de utilizar fechas recurrentes y calendario
- [ ] Ampliar el mail memory (y ver que me pasó esto como dato: Frases de los Les Luthier)
- [ ] Terminar el script para pasar audio a texto

Tengo que poder convertir formatos en celu y compu:

- COMPU Audio a texto
- COMPU Texto a audio
- COMPU Texto a imagen
- COMPU Imagen a texto
- COMPU Texto a QR
- CELU Texto a audio
- CELU Texto a imagen
- CELU Imagen a texto
- CELU Texto a QR
- CELU QR a texto
- CELU Texto a PDF

## ESTUDIAR

- [ ] Terminar curso de importación (https://importacionestrategicacomoimpo.club.hotmart.com/lesson/gOpdV3nrOJ/2.-protocolos-y-comportamientos)
