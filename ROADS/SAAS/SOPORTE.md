# 📦 ROADS > SAAS > SOPORTE

"GET /v0.2/productos/?iue=5g44JC4wcs4bMfmyPYjW&busqueda=04553&iddeposito=1&cantidad=completo HTTP/1.1"
qQvdXVbqXRZ6P0z3vui0
ZrWj7EB0mrr4mjcgjh8J
CSPG0wAJV0IWXkqmm9cC
zVmdaQHvAPoKAeAgJHGg
KxdF4jBUCIrHjqmCEeSP
oZQJ7oM4YCVJqb6SqvSd
7stpXA0BnuerchzUHzj5
5g44JC4wcs4bMfmyPYjW
hzBTnkNyCShCObMt8ifU


*******************************************************************************

## ANTIAFIP NOTA DE CREDITO

saas_3393

SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idtipoventa = 18 AND numero > 1281 ORDER BY numero LIMIT 200;

SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idventa IN (SELECT idventa FROM saas_3393.ventasxventas WHERE idrelacion = 86363);


## EMPRESAS EN BETA Y ALFA

(98, 161, 2629)
(168, 215, 255, 301, 761, 874, 901, 1363, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 2354, 3242, 3692, 3983, 4837, 5007, 5027, 5112, 6149, 6282, 6551, 6801, 7160, 7214, 7721, 7801, 8666, 9432, 9589, 9746, 9988, 10047, 10129, 10172, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12281, 12348, 12355, 12905)


## PROBLEMAS ZUMBANDO

### MAIL:

Nombre de usuario de IAM: ses-smtp-user.20240702-164504
Nombre de usuario de SMTP: ********************
Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg

NEW IAM PARA SES
AWS_KEY: ********************
AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6

EX IAM PARA SES
AWS_KEY: ********************
AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+


### LAMDA:

Acceso: https://saasargentina.signin.aws.amazon.com/console
User: empresa_9589_script_1
Password: qZbquV3&
Region: San Pablo (sa-east-1)
Lambda >> Functions: empresa_9589_script_1


## SUBDIARIOS URIEL NUEVO SÓLO VENTAS

**EXPORTACIÓN PARA INFORMES OFFLINE**

- Mandar con <NAME_EMAIL>
- Exportar desde Chrome
- Sacar el memory_limit
- Generar CSVs y guardarlos a XLSX
- Informe de Comprobantes emitidos (con Usuario, Provincia, CUIT y Datos ML y Datos Extras Ventas)
- Subdiario (sólo los impuestos de Misiones, Provincias, No Grabado, 10,5 y 21 de IVA)

prod_saas
./6149.sh
exit

cd /home/<USER>/SOPORTE/6149
./6149.sh

db

USE saas_6149;
TRUNCATE clientes;
TRUNCATE ventas;
TRUNCATE ivasxventas;
TRUNCATE tributosxventas;
TRUNCATE ventas_ml;
TRUNCATE datosxextras;
SOURCE saas_6149_ventas.sql;
SOURCE saas_6149_clientes.sql;
SOURCE saas_6149_ivasxventas.sql;
SOURCE saas_6149_tributosxventas.sql;
SOURCE saas_6149_ventas_ml.sql;
SOURCE saas_6149_datosxextras.sql;
SOURCE saas_6149_usuarios.sql;
UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';

**INFORME VENTAS ANUAL**

SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL
FROM ventas AS v
    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa
    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente
    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion
    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4
    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5
WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'
    AND tipofacturacion = 'electronico'
ORDER BY fecha

**ARCHIVO DE COMBOS**

SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad
FROM productosxcombos AS pxc
    LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto
    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto
WHERE c.combo = 1 AND c.mostrartienda = 1
ORDER BY pxc.idcombo

SELECT COUNT(DISTINCT idcombo)
FROM productosxcombos AS pxc
    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto
WHERE p.combo = 1 AND p.mostrartienda = 1


## IMAGENES GEZATEK

SELECT idproducto, nombre, codigo,
CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/imagen') AS imagen,
CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/miniatura') AS miniatura
FROM productos INNER JOIN archivos ON archivos.modulo = 'productos' AND archivos.id = productos.idproducto
WHERE publico = 1 AND mostrartienda = 1
ORDER BY idproducto


### PARA VACIAR LOGS:

- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro
sudo su
du -h / | grep '[0-9\.]\+G' > espacio
cat espacio

cd /saas/logs
> alfa_ssl_access_log
> alfa_ssl_error_log
> api-alfa_ssl_access_log
> api-alfa_ssl_error_log
> api-beta_ssl_access_log
> api-beta_ssl_error_log
> api_ssl_access_log
> api_ssl_error_log
> app_ssl_access_log
> app_ssl_error_log
> beta_ssl_access_log
> beta_ssl_error_log
> informes-alfa_ssl_access_log
> informes-alfa_ssl_error_log
> informes-beta_ssl_access_log
> informes-beta_ssl_error_log
> informes_ssl_access_log
> informes_ssl_error_log
> login_ssl_access_log
> login_ssl_error_log
> scripts_ssl_access_log
> scripts_ssl_error_log
> www_ssl_access_log
> www_ssl_error_log


### LIMPIEZA

- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)
- Vaciar los archivos de archivos_eliminar.txt
También tengo un archivo con miles de 414
mrMpouB6CwYbxYzfX4dM
YAMbM5AUUXWbjsXiC3Gz
UeO2rCGc6RDvSwQwTj33
Gkf3NV2VdsbTMWyO8cHS
eegSb4ea6Nkn3HCETMiZ
SOZ5XZbrhJpiMZPnS8M2
UJ3PBMUghUYcfeatSmsC
zDitU7SXxOJ6sgxQXqQ9
mEBenx5EYfj8hHkcu84d
x4u37Dn9rrEiKRXdeEGJ
f2KFQ5DuQSAsiT87zDIi
9ry6R98Yh4TPirTF4J80
HyDtEy8HDjTxquYQYdbq
Abn7K2fmfh7z2SXdZU8M
aQjIEVpZNqFaX3NMPoFr
FsTyDjT8Dtu6mQIomJjg
Xk0mxVTMcPM3pxDwki6U
cNcgqiRkH6krEocjaknC
DudGFpZ365KNo9Kt5PYa
s6mpAAFGd2JibdSmqxKq
NVce5qjBo3TaY8Ykksex
aMEakHabEhTzIm06Ewnm
J0isamvJiwCYShT2u2HE
Pkdw5wDfgWG8zXDzS6vC
sNVBUxXog4Jz7jukKWre


### ALTA SUCURSAL GAMING CITY
- Agregar la instancia a empresa_874_script_2.php
- Pasar la base de datos a BETA
- Sincronizar rubros y listas de precios manualmente `INSERT INTO saas_12905.productos SELECT * FROM saas_874.productos;`
- Agregar la instancia a es_franquiciado
- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`
- Activar 90 días de prueba


### CRONTAB

Scripts de la empresa Nº 99 (SaaS Argentina):
El script Nº 1 se ejecutó correctamente.
El script Nº 2 no debe ejecutarse hoy.
El script Nº 3 no debe ejecutarse hoy.

Scripts de la empresa Nº 161 (Cronometraje Instantáneo):
El script Nº 2 se ejecutó correctamente.

Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):
El script Nº 2 no debe ejecutarse hoy.

Scripts de la empresa Nº 1387 (Gezatek SRL):
El script Nº 1 se ejecutó correctamente.

Scripts de la empresa Nº 2142 (Gezatek SRL):
El script Nº 1 se ejecutó correctamente.
El script Nº 2 se ejecutó correctamente.

---

Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):
El script Nº 1 se ejecutó correctamente.

Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):
El script Nº 1 se ejecutó correctamente.

Scripts de la empresa Nº 8816 (Almacén del Bioquímico):
El script Nº 1 se ejecutó correctamente.

Scripts de la empresa Nº 8918 (ROMEO BOXES):
El script Nº 1 se ejecutó correctamente.

Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):
El script Nº 1 se ejecutó correctamente.

Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):
El script Nº 2 se ejecutó correctamente.

Scripts de la empresa Nº 11360 (Ipanema Distribuciones):
El script Nº 1 se ejecutó correctamente.
El script Nº 2 se ejecutó correctamente.

---

Scripts de la empresa Nº 874 (Gaming-City Computacion):
El script Nº 1 se ejecutó correctamente.
El script Nº 2 se ejecutó correctamente.

Scripts de la empresa Nº 6149 (Duaitek SRL):
El script Nº 1 se ejecutó correctamente.
