# 🤖 ROADS > AGENTE
-------------------------------------------------------------------------------

## 📊 PLAN

## PRÓXIMOS MILESTONES

- [x] Actualizar n8n
- [ ] Configurar app.agente.ar con un node base
- [ ] Generar página para autenticar usuario por whatsapp
- [ ] Lograr guardar un log de la página
- [ ] Auth Google con permiso a Calendar y guardar token y refresh
- [ ] Probar la conexión con el token desde n8n

- [ ] Instalar Supabase y crear base de datos para usuarios y para Remax
- [ ] Actualizar el script de Remax para que migre la información a Supabase
- [ ] MPC Servidor propio para consultar propiedades
- [ ] Averiguar por múltiples certificados
- [ ] Pensar estrategia de módulos

1) https://app.yestoki.com/settings/calendars?source=whatsapp&type=0&bot_name=%2B16502234435&code=ndq5me81wu021v13agez906cijhpvmb9vrwd5638
2) https://app.yestoki.com/settings/calendars?source=whatsapp&bot_name=%2B16502234435
3) https://app.yestoki.com/connect/calendar/google?redirect_url=https%3A%2F%2Fapp.yestoki.com%2Fsettings%2Fcalendars%3Fsource%3Dwhatsapp%26bot_name%3D%252B16502234435&provider=google&ref=app&source=app&account=andresmaiden%40gmail.com&status=success

## SELENIUM EN AGENTE.AR

- [x] Instalar selenium y correr un test
- [x] Crear repositorio git
- [x] Desarrollar un index principal
- [x] Logearse en local
- [x] Prisci con git y deploy
- [x] Deploy con GitLab CD
- [x] Logs y outputs
- [ ] Que se pueda obtener el listado
- [ ] Ejecutar con n8n
- [ ] Leer la devolución
- [ ] Analizar si se puede guardar sesión
- [ ] Pasar la pelota


## MÚLTIPLES CERTIFICADOS

- Los hilos de este tema en la comunidad son:
  - https://community.n8n.io/t/access-saved-credentials-from-expressions/857/29
  - https://community.n8n.io/t/select-credentials-via-expression/5150/39
- Posibles community nodes:
  - https://www.npmjs.com/package/n8n-nodes-run-node-with-credentials-x (este ya lo tenemos)
  - https://www.npmjs.com/package/n8n-nodes-dynamic-node


## MÁS TEMAS

La idea original la tengo en [AIC.md](./AIC.md)

## TOKENS

En el archivo CloudAPI.postman_environments.json están los datos

**Agente Pri**

EAAVk1XncURIBO9xXcdmWkQcYAYkzY7KXZC6AZA8I6DuV11SNsyrtulJzkmVySdgKgLBYw7fkZBPgg5I1vF2ZA9Io8uTZAwOSMX4agDOAEnSJbxs9kzAoirT9DjBfQ4YVAdIRPfHODnmoFuoFRPZAJbU1b1DgSAPemEf9SKlmj3Pq9t2hZArTa6cVk1dJVpFSVprOAZDZD

**Agente Crono**

EAAHC6N82t8MBO0uAmmIGM6ZAP51IWqocxkTfH4Ed6fXKwBMyViWLUciNjq2Ol4Da7UDmscBivCQjZC4f1gAbKOt7hMRbKbgNmKflbS49XHEDDHNNYZCPTXyMUUOK1tqQAp3ZAZBd36NMZAIqEjEPBRijvpoJhwh6ZCmKCpvIvUzHiB6vHqtdRZCDjG5pFCDfxBy1ZCAZDZD