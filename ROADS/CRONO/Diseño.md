# Proyecto de Rediseño de Plataformas - Cronometraje Instantáneo

Este documento contiene los lineamientos de diseño y las especificaciones técnicas para unificar la identidad visual de todas nuestras plataformas, desde el sitio web hasta las aplicaciones móviles.

## Lineamientos de Diseño de Marca

Nuestros objetivos son proyectar coherencia, profesionalidad, autoridad y modernidad, y generar un sentimiento de pertenencia en los usuarios.

Principios Generales:

- Minimalismo y Limpieza: Usar amplios espacios en blanco y diseños despejados.
- Jerarquía Visual: Usar tipografía y colores para guiar la atención del usuario.
- Énfasis en la Marca: El isologotipo y el símbolo deben aplicarse de manera consistente en todas las interfaces.
- Dinamismo y Tecnología: Utilizar el degradado corporativo en elementos clave para un toque moderno y fondos en svg para fondos de secciones destacadas.

Paleta de Colores:

- Naranja Corporativo: #FA6900 (RGB: 252, 104, 5). Color principal para acciones importantes.
- Negro Corporativo: #000000 (RGB: 0, 0, 0). Para el texto principal.
- Gris Neutro: #ADADAD (RGB: 173, 173, 173). Para elementos secundarios y fondos neutros.
- Degradado: De #FAA429 a #FA6900 para fondos de secciones destacadas.

Tipografía:

- Fuente Principal (Para Títulos y botones): Se recomienda usar Poppins o Montserrat de Google Fonts, como alternativa moderna y funcional a Myriad SemiCondensed para asegurar consistencia en la web y las apps. Pesos: SemiBold o Bold para títulos y Regular para texto de cuerpo.
- Familia 2 (Cuerpo de Texto y títulos menores): Lato o Open Sans (Regular, Light)

Sistemas de Maquetación y Espaciado:

- Sistema de Grilla: En lugar de adivinar dónde colocar los elementos, se recomienda usar un sistema de grilla que aporte estructura. Una grilla de 12 columnas es ideal para el escritorio, 8 para tablets y 4 para móviles, ya que es flexible y se adapta a distintos tamaños de pantalla.
- Sistema de Espaciado (8-Point Grid): Este sistema utiliza múltiplos de ocho (por ejemplo, 8px, 16px, 24px) para definir todo el espaciado y los tamaños de elementos. Esto crea un ritmo visual consistente y un diseño ordenado y profesional. Grandes compañías como Google y Apple utilizan este método.

Jerarquía Visual:

- Guía para la Vista: Las personas no leen, escanean. El diseño debe guiar el ojo del usuario de lo más importante a lo menos importante.
- Técnicas Clave: Puedes lograrlo usando principios como el tamaño (los elementos más grandes son más importantes), el contraste (elementos que destacan con el color o el peso de la fuente), la proximidad (agrupar elementos relacionados) y la alineación (mantener todo alineado para un aspecto ordenado y profesional).

## Especificaciones de Botones

Los botones son un elemento clave de la interacción y deben reflejar la identidad de la marca.

- Forma: Bordes ligeramente redondeados.
- Color de Fondo: Naranja Corporativo (#FA6900).
- Color de Texto: Blanco (#FFFFFF).
- Tipografía: Poppins o Montserrat en peso SemiBold o Bold.
- Estado normal (:hover): Fondo cambia a un naranja ligeramente más oscuro (#D85B04) o un degradado sutil.
- Estado deshabilitado (:disabled): Reducir la opacidad (opacity: 0.6) para indicar que no es funcional.

## Vibe Coding para Agentes de IA

Este texto se usará como guía para cualquier asistente de IA que nos ayude con la programación, para que entiendan nuestro estilo de diseño.

Soy el Director Tecnológico de Cronometraje Instantáneo. Nuestro objetivo es crear plataformas que reflejen autoridad, modernidad y tecnología, usando un diseño limpio y coherente.

Aquí están nuestras reglas de diseño:

- **Estilo:** Limpio, minimalista, moderno, tecnológico.
- **Paleta de colores:**
  - Principal: `#FA6900` (naranja vibrante)
  - Texto/Detalles: `#000000` (negro)
  - Secundario/Neutro: `#ADADAD` (gris)
  - Degradado: Desde `#FAA429` a `#FA6900` para fondos y elementos destacados.
- **Tipografía:** Usar una fuente moderna y geométrica como Poppins o Montserrat para toda la interfaz.
- **Sistema de Grilla:** Usar un sistema de grilla que aporte estructura. Una grilla de 12 columnas es ideal para el escritorio, 8 para tablets y 4 para móviles.
- **Sistema de Espaciado (8-Point Grid):** Este sistema utiliza múltiplos de ocho (por ejemplo, 8px, 16px, 24px) para definir todo el espaciado y los tamaños de elementos.
- **Iconografía:** Minimalista y con bordes suaves, reflejando la forma del ícono de la marca.

## Prompts para Generación de Elementos de Diseño

Utiliza estos prompts en herramientas de IA para crear elementos visuales que se integren con nuestra identidad de marca.

Fondos de Secciones Principales

Fondo abstracto para sitio web de cronometraje deportivo. Estilo tecnológico, moderno y limpio. Patrón de líneas y formas geométricas sutiles. Colores: #FA6900 y #ADADAD. Incorporar un degradado suave que evoque movimiento. Resolución 1920x1080px. Minimalista, vector, UI design, flat.

Botones y Elementos de Interfaz

Botón de interfaz de usuario para una plataforma de gestión deportiva. Estilo: limpio, moderno, con bordes ligeramente redondeados. Color de fondo #FA6900. Texto en color #FFFFFF. Hover y focus con un degradado sutil. Icono de un reloj o un cronómetro en color blanco. 3D render, UI/UX, flat design, vector, minimalista.

Tablas y Tarjetas de Contenido

Diseño de tarjeta para una tabla de resultados de eventos deportivos. Estilo: limpio, elegante, profesional. Fondo de la tarjeta en un gris claro #F5F5F5 con un borde de 1px en #ADADAD. Cabecera de la tabla con fondo degradado de #FEFEFE a #FA6900. Tipografía Poppins. Minimalista, UI design, vector, light background.
