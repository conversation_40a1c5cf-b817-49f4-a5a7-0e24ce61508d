# 🏅 ROADS > CRONO > PLAN

## INTRO

Este es un documento interno, escrito por Andres Mi<PERSON>k principalmente como planning personal, desde el comienzo del 2024 y hasta fin del 2026 (3 años), pero también para compartir a los que también trabajan en este proyecto (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> y <PERSON>). La idea es cerrar una empresa con todos los procesos bien aceitados y lo más posible automatizado.


## ÍNDICE

- [🏅 ROADS \> CRONO \> PLAN](#-roads--crono--plan)
  - [INTRO](#intro)
  - [ÍNDICE](#índice)
  - [HISTORIA](#historia)
  - [CRONOMETRAJE INSTANTANEO EN 2024](#cronometraje-instantaneo-en-2024)
  - [CRONOMETRAJE INSTANTANEO HASTA 2026](#cronometraje-instantaneo-hasta-2026)
    - [Nuevo sistema](#nuevo-sistema)
    - [Deportes](#deportes)
    - [Eventos](#eventos)
    - [Micro sitios](#micro-sitios)
    - [Cursos](#cursos)
    - [Herramientas y Hardware](#herramientas-y-hardware)
    - [Serverless](#serverless)
    - [AgenteCrono](#agentecrono)
    - [EventosIA](#eventosia)
  - [CRONOMETRAJE INSTANTANEO DESPUÉS DEL 2026](#cronometraje-instantaneo-después-del-2026)
  - [MERCADO](#mercado)
    - [Competencias](#competencias)
    - [Tipos de clientes](#tipos-de-clientes)
  - [PRODUCTOS Y SERVICIOS](#productos-y-servicios)
    - [Servicio principal](#servicio-principal)
    - [Hardware](#hardware)
    - [Micrositios Gratis](#micrositios-gratis)
    - [EventosIA](#eventosia-1)
  - [PLAN DE DESARROLLO](#plan-de-desarrollo)
  - [PLAN DE MARKETING](#plan-de-marketing)
    - [¿Cómo llegan los clientes?](#cómo-llegan-los-clientes)
    - [Estrategia de Loop Marketing](#estrategia-de-loop-marketing)
    - [Estrategia SEO/AEO](#estrategia-seoaeo)
    - [Estrategia de tecnologías](#estrategia-de-tecnologías)
    - [Estrategia de deportes](#estrategia-de-deportes)
    - [Estrategia de contenido](#estrategia-de-contenido)
    - [Ideas para contenidos](#ideas-para-contenidos)
    - [Estrategia de Publicidad](#estrategia-de-publicidad)
    - [Estrategia de Internacionalización](#estrategia-de-internacionalización)
  - [PLAN DE VENTAS](#plan-de-ventas)
  - [PLAN DE SOPORTE](#plan-de-soporte)
  - [PLAN FISCAL](#plan-fiscal)
  - [MAPA DE RIESGO Y PLAN DE CONTINGENCIA](#mapa-de-riesgo-y-plan-de-contingencia)
    - [RIESGOS PRINCIPALES](#riesgos-principales)
    - [RIESGOS MENOS PROBABLES](#riesgos-menos-probables)


## HISTORIA

Podemos dividir la Historia de la empresa en estas etapas:

1. **Cronometradores**. Lo usabamos nosotros mismos en AAVLA y para carreras en la zona. Notamos que teníamos los domingos ocupados y había mucho trabajo manual, con poca posibilidad de crecimiento. En ese momento teníamos las secciones de *Cronometraje* (Andy) y *Atención al cliente* (Sandoval).
2. **Sistema de cronometraje**. Pasamos a vender el software brindando poco servicio adicional. Pudimos empezar a crecer pero sin ganancia. Nos dividimos en *Programación* (Andy) y *Venta* (Sandoval). Comenzamos a trabajar con un equipo dedicado en Ecuador y a tratar de entender el mercado y lo que realmente se necesitaba de nosotros.
3. **Gestión completa para cronometradores y organizadores** (**Estamos ACÁ**). En la pandemia decidí dedicarme casi fulltime a Cronometraje Instantáneo y luego de dos años de mucha inversión en programación, compra de equipos y generación de mercado, logramos una cantidad de clientes distribuidos en casi todos los países de nuestro interés, reconocimiento de marca, que la empresa tenga rentabilidad y un flujo contínuo de eventos. Trabajando con un pequeño equipo muy eficiente: en *Desarrollo y Growth* (Andy), *Ventas* (Juli), *Capacitación y soporte* (Gaby), *Marketing* (Prisci), *Ecuador* (Francisco y Felipe). En esta etapa logramos tener todas las herramientas para poder generar cronometrajes completos con chips, fotocélulas y apps. Logramos terminar un sitio web acorde y un pequeño sector de cursos.
4. **AgenteCrono y EventosIA**. Durante el 2025/26 vamos a crecer generando un nuevo sistema con Inteligencia artificial, mejorar la distribución de servidores, modernizar las herramientas actuales y empujar los cursos y el marketing de contenido.

> 💡 **Visión**: Promover el deporte como estilo de vida, potenciando a los eventos deportivos y mejorando la experiencia del staff y sus participantes.

Anterior visión (pre-pandemia): Liderar el mercado latinoamericano de la gestión y cronometraje de eventos deportivos. Con soluciones tecnológicas, innovadoras y apropiadas, <b>a través de una red de aliados</b>.

>  💡 **Misión**: Brindar soluciones tecnológicas y <b>conocimiento</b> para mejorar, simplificar y automatizar procesos en la organización integral de eventos deportivos y el cronometraje.

Anterior misión (pre-pandemia): Brindar soluciones y <b>conocimiento</b> para obtener, procesar y exponer los resultados de los eventos deportivos al mundo, con <b>calidad profesional, precisión y al instante</b>. Desarrollando tecnología innovadora aplicada a la <b>realidad latinoamericana</b>. Porque sabemos que es posible.

>  💡 **Valores**:
> - Work–Life Balance
> - Know-how
> - Innovación
> - Honestidad
> - Trabajo en equipo

**Puntualmente lo que vendemos es un sistema de gestión online y apps, con herramientas para cronometradores y organizadores de eventos. Cobramos la licencia del sistema por evento y como un servicio ya que damos valor agregado con mucho acompañamiento por Whatsapp.**


---

## CRONOMETRAJE INSTANTANEO EN 2024

Estas son algunas situaciones que estoy considerando:

- Creo que el software va a cambiar radicalmente en los próximos años, reduciendo cada vez más el uso de pantallas y cambiando las interfacez por chats y comandos de vos.
- Pudimos avanzar mucho estos últimos 2 años en las herramientas para cronometraje, pero nuestro módulo de inscripciones es bastante básico y nuestro módulo de gestión de eventos es malísimo con mucho trabajo manual de mi parte.
- Sería bueno despegarnos de la competencia con alguna herramienta nueva e innovadora, especialmente para poder atraer a organizadores y cronometradores que todavía no son clientes con algo único.


## CRONOMETRAJE INSTANTANEO HASTA 2026

Para completar el plan de crecimiento, dividí en varios proyectos para ir encarando uno a uno. El objetivo es para final del 2026 que la empresa tenga todas las herramientas de administración y capacitación bien cómodas y bastante automatizadas. Que hayamos impactado en el mercado de micro nichos por cada deporte y ya con algunas herramientas de AI.

### Nuevo sistema

Para empezar voy a programar un nuevo sistema con un nuevo Framework que va a darnos un software más moderno, pero principalmente que pueda programar más rápido, ya que nunca pude resolver tener otro programador en la empresa. El Framework que elegí es Filament 3 que está sobre Laravel 10. En este nuevo sistema vamos a comenzar por tener una nueva y completa gestión de usuarios, organizaciones, cronometradores y eventos.

### Deportes

Creo que la mejor forma de crecer es apuntar a micro nichos. Se basa en centrarse en un segmento específico del mercado, creando productos y servicios especialmente diseñados para responder a sus necesidades y preferencias. En nuestro caso son los deportes. Cada organizador de un deporte está bien metido en ese deporte y tiene que ver que entendemos sus necesidades bien puntuales y que podemos ayudarlo.

Creo que con algunos cambios en el diseño y en el UI, podemos generar que el sistema tenga una versión para cada deporte, personalizando las configuraciones y los textos para que sean más intuitivos y fáciles.

Vamos también a hacer páginas específicas para cada deporte y marketing de contenido para cada uno.

Voy a trabajar bien el plan en [PLAN DEPORTES](PLAN%20DEPORTES.md).

Queremos terminar con esta completa gama de servicios para cada deporte

```
La solución completa para eventos de Rally cuenta con:
- Micrositio Web para el evento
- Micrositio Web para la organización
- Sistema para organizadores de Rally
- Sistema para cronometradores de Rally
- Sistema para pilotos de Rally
- Equipos para cronometraje de Rally
- Cursos y capacitación personalizada a todo el personal
- Soporte técnico y acompañamiento durante el evento
- Agente de Inteligencia Artificial
```

### Eventos

Desde hace muchos años que nos falta un sector para mostrar todos los eventos que tenemos en el sistema. Esto nos va a servir para posicionarnos de forma orgánica y mejorar la experiencia de los participantes que hoy se la pasan preguntando por los resultados. Además nos abre paso para el próximo paso de los micro sitios.

### Micro sitios

Otra necesidad que veo en el mercado, es la de los sitios webs. Hoy la mayoría no tiene sitios y los que tienen usan Wordpress con diseñadores que no son económicos. Sumado a que la gente hoy usa más Instagram que los sitios webs, pero como es una red social, no hay forma fácil de dejar la información ordenada. Muchos lo resuelven con un Linktree.

Entonces la idea es ofrecer una solución para este problema con un producto intermedio. Un software para hacer sitios webs bien simples. Los sitios van a ser mejores que un Linktree pero más simples que un Wordpress.

La idea es ofrecerlo como un software totalmente gratuito y sin publicidad. Esto nos va a servir para mejorar la información de nuestros eventos, para posicionarnos de forma orgánica y para tener una muestra gratis que un equipo de ventas pueda ofrecer.

### Cursos

El material y la plataforma de cursos que hicimos funcionó muy bien pero se quedó demasiado corta. Aprovechando el nuevo sistema y el empuje que tenemos que lograr grabando vídeos para marketing, voy a grabar nuevos cursos con mucho más material.

### Herramientas y Hardware

Con respecto a las Herramientas actuales, si bien ya tenemos todo lo necesario para realizar todo tipo de cronometrajes, hay mucho por mejorar, por lo que la idea es seguir con los siguientes temas:

- Re-programar todo lo de los chips en la versión de la APP 3.x
- Empezar con pruebas reales con Beacons
- Agregar una función de Kiosko en la app
- Agregar compatibilidad para los equipos: Macsha, Chafón, Pistola RFID, Chips Activos
- Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos
- Mejorar el Modificar tiempos
- Agregar un sector para administrar Chips y Beacons

### Serverless

Este año logré mejorar bastante la estabilidad del servidor, principalmente con el sistema de cache que programé. Pero vamos a necesitar algo más robusto y que me requiera menos tiempo administrando servidores. Además el módulo de vivos/streaming no quedó como yo esperaba, teniendo incluso alguna fallas que no pude reparar.

> Por eso, la idea es separar el servidor actual en diferentes servicios:
> - Cloud MySQL: para los datos relacionados
> - Cloud Storage: para los datos estáticos web, calendarios y eventos (micro sitios)
> - Cloud CDN: resultados
> - Firestore para las lecturas y los tiempos
> - Cloud Run con Containers para: chat + eventos.ai, admin + api + vivos
> - Cloud Functions para los scripts, queues y procesos varios

### AgenteCrono

La idea es programar un sistema totalmente nuevo, que utilice Inteligencia Artificial para la comunicación con los organizadores y los participantes.

En un primer lugar, va a ser el nuevo módulo de gestión de eventos, donde mediante un chat, se va a poder crear nuevos eventos, modificar los actuales y gestionar los pagos de los mismos, reemplazando las actuales ventanas de "Historial", "Nuevo evento" y "Configuración > Evento" (sin la parte de diseño). Después voy a agregar las ventanas dentro de "Configuración > Inscripciones". No tengo pensado hacerlo para el módulo de cronometraje por el momento.

Este nuevo módulo va a tener solamente un chat, sin ventanas ni botones (tengo que lograr que sea completamente funcional de esta forma).

El módulo por si mismo, tiene que poder generar un micrositio del evento, datos de contacto, todos los enlaces de informes y los enlaces a resultados. También tiene que poder gestionar los pagos del sistema por distintas pasarelas. Tiene que poder brindar esa información al organizador, para que de esta forma, no necesiten aprender a usar al sistema, y hasta ni siquiera tener acceso al mismo. El chat hay que conectarlo a Whatsapp.

Como segunda etapa, voy a hacer que el mismo chat, pueda atender también a los participantes, permitiendo consultar un calendario de eventos, inscribirse a cualquiera de ellos con sus datos pre-grabados o por primera vez, pagar por sus inscripciones y hasta consultar datos generales del evento y de los resultados. También todo solamente desde un chat, sin ninguna ventana ni botón.

Como tercera etapa, voy a pasar la información de los cursos también para que el mismo chat pueda ayudar con las capacitaciones.

### EventosIA

Estoy imaginando un sistema muy innovador y disruptivo, que me va a llevar muchísimo trabajo desarrollarlo. Por este motivo no creo que pueda "recuperar" esa inversión solamente con el crecimiento de la cartera de clientes de Cronometraje Instantáneo. Y además sería una picardía "cerrar" el mercado sólo a eventos deportivos. Es por eso que me gustaría generar una nueva unidad de negocio, con el mismo equipo de trabajo y la misma estructura de servidores, pero con un mercado mucho más amplio y una posibilidad de ganancia para todos mucho mayor.

El sistema lo voy a programar desde el primer momento para que sea compatible con cualquier tipo de evento: reuniones, recitales, teatros, etc. Voy a generar una segunda marca y un producto independiente para la gestión de eventos (publicación /micrositio, inscripciones / venta de entradas, cobros y acreditaciones / control de ingreso). Con la experiencia que vamos a obtener de los eventos deportivos y si llegamos antes que los grandes sistemas de tickets, podemos armar un plan para atacar ese otro mercado mucho más grande.


## CRONOMETRAJE INSTANTANEO DESPUÉS DEL 2026

Si bien falta mucho y no vamos a planear la siguiente etapa. La idea es después de lograr todo el crecimiento de este plan, formular otro plan de expansión en la cual haya encargados por país o región que controlen la empresa y nos reporten a nosotros. Quizás podamos sumar otras regiones, idiomas o incluso expandirnos a eventos no deportivos, ... veremos.


---

## MERCADO

### Competencias

Tenemos un listado de todas las competencias que conocemos en [Mercado de software de cronometraje](https://docs.google.com/spreadsheets/d/16y0ROgBdJ-1JYr66mWzIHYINIFPWCF9hn4x2VP33uMU/edit?usp=sharing)

El mercado es muy grande pero lo noto dividido entre distintos deportes e idiomas.

Hay empresas muy grandes en Europa y USA que arrancaron en el siglo pasado con muchos clientes, pero con un modelo de negocio en el que ellos te venden el hardware y el software cerrado que sólo funciona con sus productos. Están especialmente orientados a *running*, *ciclismo* y *triatlón*. Y tienen un modelo de negocio que no se adapta a la realidad de Latinoamérica, por los altos precios.

Por otro lado están los deportes que se cronometran con fotocélulas (como *downhill*, *rally*, *kayak*, etc), donde claramente no hay muchas opciones y estamos liderando el mercado en Latinoamérica.

El tema del idioma es muy fuerte, las empresas que ofrecen capacitacion y soporte en español son muy pocas y ahí es donde nosotros estamos entrando.

Nuestro enfoque es dar mucha capacitación y acompañamiento en español que es lo que la mayoría necesita. Y ofrecer un sistema dentro de todo económico, con soluciones que utilicen mayoritariamente el celular y sino con hardware accesible.

Hoy somos muy fuertes en Ecuador y Argentina. Tenemos precensia en Chile, Perú, Venezuela y Colombia. Y algunos pocos clientes sin ser todavía reconocidos en: México y Centro América.

Durante el 2023 se estableció más Webscorer en nuestro mercado y tanto Timingsense como RaceId están intentando entrar. Ninguno tiene un acompañamiento como el nuestro, pero tenemos que lograr seguir delante de ellos con soluciones reales y soporte real.


### Tipos de clientes

En esta etapa de la empresa distinguimos 3 tipos de clientes bien diferenciados

- **Cronometradores**: son empresas que ya tienen sus soluciones pero quieren mejorar y otras que quieren arrancar en el negocio de cero.
- **Organizadores de eventos que buscan servicio**: están buscando justamente a los cronometradores del primer punto, nosotros coordinamos con ambos fechas disponibles y necesidades.
- **Organizadores auto-cronometraje**: son organizadores chicos que no pueden pagar un servicio de cronometraje y les enseñamos a hacerlos ellos mismos.

En los 3 casos, suelen ser deportistas o ex-deportistas que fueron ingresando al mundo de la organización de eventos y que tienen mucho conocimiento de los deportes que organizan, pero generalmente poco conocimiento de tecnología. Todos buscan hacer crecer su empresa más allá de mejorar sus ingresos.

La edad es siempre de entre 25 y 50 años, y la gran mayoría son hombres.

Buscan mucho en internet y miran todo lo que hacen sus competidores, tanto organizadores de eventos como empresas de cronometraje.


---
## PRODUCTOS Y SERVICIOS

### Servicio principal

Se podría resumir que vendemos un servicio en donde les enseñamos el oficio por Whatsapp, específicamente usando nuestras apps y plataforma online, con distintas combinaciones de hardware (la mayoría vendida por otros).

El sistema tiene que dar la sensación de ser *un poco* caro, pero que vale la pena. Para eso se requiere mucha re-inversión y trabajo en desarrollo. Además de estar siempre atrás del Whatsapp para ayudarlos.

Antes teníamos 3 propuestas básicas de precios, para 3 nivéles de eventos. Desde ahí se dan comisiónes o descuentos a **nuestros cronometradores**. Teníamos una [Lista de precios](https://docs.google.com/spreadsheets/d/1IMn6w-ULJUxJPUr-oIpN-te1o0wVzNE87tH2k7SYRJ4/edit?usp=sharing), pero la gran mayoría paga por cada evento U$D100 menos el descuento por cronometrador, ya que es lo que más ofrecemos y tratamos de vender. Siendo el ticket promedio unos U$D60 (o sea que damos muchos descuentos y algunos eventos sin cargo), no siendo algo totalmente malo ya que la fidelidad de los clientes que pagan menos es muy fuerte.

En el 2025 decidimos simplificarlo en un sólo precio para cada uno de los tipos de clientes:

- Auto-cronometraje: U$D 120 por evento
- Organizadores: U$D 100 por evento
- Cronometradores: U$D 80 por evento

Sobre estos precios vamos a ir sacando los descuentos existentes y ofreciendo promociones de pagos por varios eventos.

Agregamos **Garantía completa** durante el 2021 y funcionó muy bien: *"Si no te gustó el servicio o se suspende tu evento te devolvemos el dinero"*.

El cobro por participante (utilizado por la mayoría de nuestras competencias de otros continentes) lo vemos complejo para que el cronometrador pueda hacer un presupuesto claro y aceptable por los organizadores. Preferimos hacer descuentos en eventos chicos.

No podemos olvidar que Webscorer suele ser más económico que nosotros, o por lo menos esa es la percepción que se ve en su sitio web.

### Hardware

Comenzamos a vender fotocélulas que mandamos a fabricar nosotros mismos y hemos vendido un montón. Durante el 2022 también sumamos algunos equipos de RFID que traemos de China y la intensión es hacer crecer esta rama del negocio, pero por el momento sin mucha promoción, ya que lo hacemos en negro. La venta solamente sería para Argentina por nosotros y para Ecuador por Francisco (sin comisión para Argentina en este mercado).

### Micrositios Gratis

Tengo planeado ofrecer un servicio de Micrositios webs gratis para cualquier evento deportivo. La idea es tener un producto de primera entrada, para captar prospectos y poder ampliar el mercado. Sería sin soporte, todo con AgenteCrono y Serverless para que no afecte de ninguna forma al soporte y cronometraje actual.

### EventosIA

Si logramos este año tener el servicio de inscripciones de AgenteCrono pulido y testeado, y además logramos tener este segundo producto, ahí tenemos otra fuente de ingreso que puede ser muy grande, por lo que vamos a tener que re-estructurarnos para poder atender este nuevo mercado.

Vamos a necesitar mucho trabajo de revisión de conversaciones, por lo que probablemente haya que sumar a alguien más al equipo. Además, las ventas de este nuevo producto/servicio va a necesitar vendedores a parte. Más adelante escribí algunas ideas sobre este tema.

---

## PLAN DE DESARROLLO

Las prioridades de desarrollo al arrancar el año, teniendo en cuenta que pueden cambiar como siempre sucede, son así:

1. Agregar una función de Kiosko en la app
2. Agregar compatibilidad para los equipos: Macsha xFTP, Chafón x2
3. Actualizar framework y mejorar diseño
4. Mejorar el Modificar tiempos
5. Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos
6. Generar procesos con pasarelas de pagos automatizadas (en eventos y para los participantes)
7. Generar ventanas para configurar de eventos, organizadores y cronometradores
8. Generar ventanas para la creación de Micrositios (sólo para usuarios al principio)
9. Rediseñar sistema con Deportes
10. Generar sector de Eventos en el sitio
11. Generar sector de Deportes en el sitio
12. Migrar base de datos relacional a Cloud MySQL
13. Generar Cloud Run con Containers
14. Generar los procesos en Cloud Functions para los datos estáticos
15. Generar los procesos para exportar los resultados a CDN
16. Lograr primera etapa de AgenteCrono para la gestión de eventos y micrositios
17. Desarrollar toda la gestión de pagos dentro de AgenteCrono
18. Re-programar todo lo de los chips en la versión de la APP 3.x
19. Conectar AgenteCrono a Whatsapp
20. Lograr segunda etapa de AgenteCrono para las inscripciones de participantes y sus pagos
21. Empezar con pruebas reales con Beacons
22. Agregar un sector para administrar Chips y Beacons
23. Agregar compatibilidad para: Pistola RFID, Chips Activos
24. Generar sector de cada Hardware en el sitio


---
## PLAN DE MARKETING

### ¿Cómo llegan los clientes?

Hoy nos llegan clientes principalmente por 2 vías:
- Una de ellas es porque vieron nuestro software en un evento y nos contactan
- La otra es porque buscan en Google o Youtube sobre algo referido al cronometraje (en menor medida nos ven en las redes)

### Estrategia de Loop Marketing

En todas las inscripciones, los resultados y los tickets digitales que se generan con nuestro software, se incluye un link a nuestra página web. Por lo tanto, todos los participantes pueden acceder y contactarnos. Para que esto suceda tenemos que tener buenas herramientas y que los cronometradores sean buenos en su trabajo, para que se resalte el servicio y se interesen en como lo hacen. Pero eso no tiene mucho que podamos hacer desde el lado del marketing.

### Estrategia SEO/AEO

Por lo que si podemos hacer mucho es para posicionarnos en Google. Para eso tenemos estructurado el sitio para SEO y tenemos que estar continuamente generando contenido para que sea interesante para el visitante, pero también que se posicione bien.

Con respecto al back-link estamos más bien a mi parecer, ya que todos los posteos y páginas de los organizadores, junto con muchos diarios, apuntan a las inscripciones y resultados que están en nuestra página, específicamente programada para eso.

Como tareas para mejorar el SEO pueden ser:
- Revisar arquitectura de metadatos, mapa del sitio, etc.
- Generar los sitios para cada disciplina
- Generar los sitios para cada hardware
- Generar un sector del sitio donde listamos todos los eventos y organizaciones que trabajan con nosotros
- Generar un sector de cursos y listar todos los que tenemos
- Revisar las principales entradas del blog
- Generar más entradas al blog con los temas que más buscan los clientes

### Estrategia de tecnologías

Muchos buscan directamente cronometraje con Chips, que es sólo uno de los equipos que se pueden usar y no siempre el indicado. Pero la idea es posicionarnos en todo tipo de cronometraje. Es por eso que este 2023 hicimos una página para cada uno de los equipos que vendemos (con su correspondiente vídeo hecho con un filmaker contratado).

### Estrategia de deportes

Como notamos que cada disciplina es un mundo, vamos a adoptar la estrategia de enfoque de MICHAEL PORTER. Se basa en centrarse en un segmento específico del mercado, creando productos y servicios especialmente diseñados para responder a sus necesidades y preferencias.

Ver más de este tema en [PLAN DEPORTES](PLAN%20DEPORTES.md)

### Estrategia de contenido

Generar contenido de valor para enseñar el oficio. La intensión es convertirnos en autoridad de la organización de eventos deportivos y el cronometraje.

Los vídeos van a estar preparados para Youtube y para redes sociales, separando vídeos con mucha información y bien producidos, junto con vídeos cortos con tips y consejos.

Las métricas que podemos usar son:
- Cantidad de contenidos generados
- Calidad de contenido generado (por ej. poner puntaje para calidad de vídeo, valor real brindado, impacto o reacción generada)

### Ideas para contenidos

¿Qué son las Fotocélulas?
¿Qué son los equipos de Chips (RFID UHF Pasivo)?
¿Qué son los transporders (RFID UHF Activo)?
¿Qué es una Nube híbrida?
Cronometraje con GPS con Anube
¿Qué equipos necesitas comprar?
¿Cómo comprar un equipo de chips?
¿Cuales son las modalidades de MTB y cómo se cronometran?
¿Quién es Andres Misiak?
¿Cómo funcionan los códigos QR y cómo los podes usar en cronometraje?
¿Cómo se cronometra un evento de descenso?
¿Cómo se cronometra un evento de rally?
¿Cómo se cronometra un evento de kayak?
¿Cómo se cronometra un evento de running?
¿Cómo se cronometra un triatlón?
¿Cómo presupuestar un servicio de cronometraje?
¿Qué es un Pump Track y como se cronometra?
¿Cómo gestionar las inscripciones de un evento deportivo?
¿Qué tener en cuenta al organizar un evento deportivo?

Tengo más de 200 ideas anotadas pero creo que estas son las más buscadas y prioridades

### Estrategia de Publicidad

La publicidad paga aumentó muchísimo en los últimos años, por lo tanto la idea es generar unas campañas genéricas en Google Ads solamente. Luego por objetivos específicos se pueden hacer campañas en un sector o deporte, pero midiendo bien el ROI de cada una de ellas.

### Estrategia de Internacionalización

A través de un análisis de un mercado de deporte y país específico, podemos ver cuánto mercado tenemos y cuánto podemos crecer. Si vemos que hay un mercado interesante, podemos hacer una campaña de marketing específica para ese país, con un contenido específico, una página web específica y/o contactar a los organizadores de eventos de ese país.


---

## PLAN DE VENTAS

Llegamos a un momento en el que las funcionalidades que ya tiene el sistema pueden ser vendidas más masivamente, especialmente las que son para organizadores. Los servicios a cronometradores seguiremos ofreciéndolo sólo a los interesados, cuidando el mercado y que cumplan con la capacitación/evaluación de que son profesionales.

Espero durante el 2024/2025 completar las funcionalidades restantes para que el sistema sea mucho más atractivo para organizadores con: Micrositios, mejores pasarelas de pago en inscripciones, calendario de eventos y por último la gestión de eventos e inscripciones con AgenteCrono.

Cuando tengamos Micrositios gratis, sería bueno genera una campaña informativa contactando a cada uno de los organizadores.

Luego cuando tengamos EventosIA, hay que encarar un Plan de Ventas de verdad. Ahí vamos a dividir 2 etapas para las ventas. La primera es reactiva, es decir que con marketing orgánico y publicidad, vamos a traer potenciales clientes al embudo para que se comuniquen con nosotros al Whatsapp y la segunda va a ser más proactiva, invirtiendo en múltiples campañas de marketing y realizando procesos de ventas contactando a los prospectos obtenidos desde distintas fuentes.

Para esto creo que puede ser útil contratar algún vendedor freelance. El vendedor va a obtener información de los que se comunican, informarlos sobre las ventajas de nuestras opciones y generar nuevos clientes (usuarios en el sistema). Quizás el vendedor puedas ser el mismo AgenteCrono, veremos que tan potente termina siendo.

En caso de tener vendedores humanos freelancer, se pueden acordar los siguientes puntos:

- La venta arranca cuando el prospecto se contacta al Whatsapp y termina cuando se crea un nuevo usuario en el sistema. El vendedor tiene que crear un grupo de Whatsapp y agrega a el/los capacitadores en caso de que sea para cronometraje o guiarlo a AgenteCrono para los organizadores, o a EventosIA para los eventos no deportivos.
- Los vendedores tendrán autonomía y responsabilidad sobre cómo llevar a cabo la venta, pudiendo dedicar más o menos tiempo dependiendo de la necesidad.
- Identificar que tipo de cliente es y qué es lo que realmente quiere/necesita. Registrarlo en algún CRM.
- Informar a todos de todas nuestros servicios y ventajas, tentando a sumarse a nuestro equipo. Si todavía no es el momento, registrar cuando hay que volver a contactarlo y hacerlo en su momento.
- Los que ya estén “calientes” y “en fecha”, lograr generar el usuario aunque sea para un Micrositio Gratis.
- En caso de no lograr la venta, consultar el motivo para que podamos analizarlo, mejorar y re-contactarlo.
- Las ventas van a estar apoyadas con varias respuestas precargadas, páginas en nuestro sitio web, notas del blog y vídeos de Youtube.
- Los vendedores se comprometen a tener reuniones semanales o quincenales para presentar los avances en las ventas, aportar ideas para mejorar la empresa y pedir lo que necesiten para poder desarrollar mejor su trabajo.
- Las métricas pueden ser las siguientes:
  - Nuevos prospectos encontrados
  - Prospectos no vendidos y sus razones
  - Usuarios generados.
  - Churns (con análisis aparte)


---
## PLAN DE SOPORTE

Por los servicios que ofrecemos, la capacitación es parte fundamental, es algo que no podemos dejar al azar o a cargo del cliente para que lo haga correctamente. Durante el 2023 logramos cumplir con este plan gracias a Gaby y Felipe. De todas formas dejo algunas ideas que tenía anotada a principio de este año.

El objetivo es liderar la capacitación de los nuevos clientes para que el evento sea cronometrado sin errores y que tenga la exposición que necesitamos para el loop marketing.

El material utilizado son cursos escritos y grabados en nuestro sitio web y videos de YouTube. Probablemente los tengamos que mover dentro del sistema y ojalá podamos pasarlos a AgenteCrono antes del fin de 2024.

La organización y el control de las capacitaciones lo estuvimos realizando en la planilla: [CRONO Capacitación](https://docs.google.com/spreadsheets/d/1Vg_aCYhDbb3zb_ArQ4VraviVfMF7YQblU93FvM31ji4/edit#gid=754196915) pero ya quedó obsoleta.

El acuerdo con los capacitadores tiene los siguientes puntos:

- La capacitación arranca cuando se logra una nueva venta y por ende se crea un nuevo usuario en el sistema. El vendedor crea un grupo de Whatsapp y agrega a el/los capacitadores.
- Los capacitadores tendrán autonomía y responsabilidad sobre cómo llevar a cabo la capacitación, pudiendo dedicar más o menos tiempo dependiendo de la necesidad.
- Los objetivos de la capacitación son:
   - Que el organizador aprenda a configurar las inscripciones y verificar que estas queden todas correctas antes de la fecha especificada para abrir las inscripciones.
   - Que el organizador y su equipo aprendan a administrar las inscripciones y las acreditaciones.
   - Especificar un Responsable de Cronometraje y que este pueda hacer un plan de cronometraje con las herramientas precisas, con el personal capacitado y con conocimiento para detectar y corregir posibles errores.
   - Capacitar a los cronometradores y que ellos dependan del Responsable de Cronometraje.
- Las métricas pueden ser las siguientes (por ahora no tenemos, y no parece ser necesario):
  - Satisfacción del organizador una vez terminado el evento.
  - Cantidad de problemas o errores durante el cronometraje.
  - Si hubo necesidad de que utilicen soporte técnico durante el evento.
- La capacitación se va a realizar dentro de un grupo de Whatsapp con el cliente organizador como co-administrador del grupo para que pueda agregar a todas las personas que quiera. Recordemos que cuanto más personas conocen y saben usar Cronometraje Instantáneo, mejor es para la empresa. En caso de ser necesario también se pueden coordinar reuniones por Meet para profundizar en ciertos temas o hacer pruebas en conjunto.
- Durante el primer evento (es ideal en realidad que siempre sea así), tenemos que tener alguien conectado para dar soporte. No es responsabilidad del capacitador o del soporte resolver los problemas, sólo estar para explicar como solucionar esos problemas y en todo caso avisar al líder de desarrollo para que solucione problemas graves.
- La intensión es que los cursos y luego AgenteCrono en algún momento ayuden mucho al capacitador para que el trabajo, sea más de monitoreo y acompañamiento, y no de profesor.


---

## PLAN FISCAL

Vamos a dividir los ingresos en 3 empresas fiscales diferentes:

- Monotributo: para los ingresos de Argentina (cargar impuestos separados)
- Payoneer: para los ingresos de representantes de Países (Ecuador, Colombia, etc.)
- Cronometraje Instantáneo LLC: la empresa que ya tenemos en USA, que es la que factura a los clientes del resto de los países (cargar gastos separados)

---

## MAPA DE RIESGO Y PLAN DE CONTINGENCIA

El mapa de riesgo es una lista de probabilidades que nos pueden afectar. Para cada una de ellas, hay un plan de cosas a tener en cuenta o desarrollar.

### RIESGOS PRINCIPALES

- **ÍNDICE CAMIÓN** (*¿Qué pasa si me pisa un camión? Este es el riesgo principal actual, hay varias cosas que dependen de mí*)
  - Tenemos que automatizar los procesos lo mayor posible y generar las herramientas para la Gestión Completa del sistema por los Administradores (Juli, Gaby, Francisco y Felipe), idealmente antes de que termine el 2026 que quiero terminar este plan.
- **OTRA PANDEMIA MUNDIAL** (Si se cancelen eventos o estén prohibido hacerlos)
  - La intención es tener los gastos fijos lo más bajos posible y tener gastos variables.
  - Vamos a invertir mucho en migrar todo a serverless, ya que a largo plazo, reduce el costo de servidores sin uso.
  - Además hay que lograr una empresa de pocos empleados, en todo caso contratando freelancers para cosas puntuales.
  - En caso que suceda, utilizar ahorros personales para mantener la estructura durante las temporadas de eventos cancelados (como hice en la pandemia).
- **COMPETENCIA ECONÓMICA** (*Si sale al mercado una opción más útil y económica*)
  - Voy a seguir re-invertiendo mucho en desarrollo para que nuestra plataforma tenga las mejores herramientas del mercado.
  - Generar fidelidad de parte de los cronometradores.
  - Lograr una economía de red, en la que los participantes exijan nuestra plataforma.
  - Tenemos algo de margen para bajar los precios como última instancia.
- **CRONOMETRADORES INCOMPETENTES** (*Si los usuarios cometan errores y/o hablen mal de nosotros*)
  - Dejar en claro quien es el responsable del cronometraje.
  - Felicitar a cada cronometrador exitoso y darle el crédito por su trabajo.
  - Desarrollar cada vez más herramientas para facilitar la tarea del cronometraje y el control de los resultados.
- **SERVIDOR CAÍDO** (*Si se cae el servidor y nos quedamos sin sistema*)
  - Ya tengo alertas y monitoreo de los recursos, pero la idea es migrar lo mayor posible a Serverless (las caídas en ese caso son sobre procesos puntuales y no generales).
  - Hay que agregar funcionalidades de logs externos con capacidad de re-generar los resultados offline manualmente.
- **ERROR EN LA APP** (*Si sale una nueva versión de la app con un error grave*)
  - Para esto ya tenemos todas las app correctamente versionadas, historial de novedades y un sector con la versión alternativa anterior disponible para descarga siempre.


### RIESGOS MENOS PROBABLES

- ROBO O JUICIO DE EMPLEADO (*Si nos roban código, clientes, o nos hacen juicio*)
  - No tener nuevos empleados sino proveedores/freelancers a comisión por trabajo (excepto los actuales que son de extrema confianza).
  - Tener contrato con todos los que no son de confianza (incluyendo de confidencialidad).
  - Dar sólo la información que necesita para trabajar y sólo de su sector.
- MKT (*Si no funciona o hay una competencia que invierte más fuerte*)
  - Tener opciones de comunicación que sean directas a nuestros clientes para mantenerlos con propuestas específicas.
- COMPETENCIA A LATINOAMÉRICA (*Si alguna de las europeas o yankis busca este mercado con sus soluciones traducidas*)
  - Conocer y seguir a todas las competencias. Si es posible comprar sus equipos y compatibilizarlos mejor. Si es posible comunicarse y conocerlos en persona también mejor.
- ECONÓMICO (Si me quedo sin dinero)
  - Lograr la auto-gestión completa por parte de los usuarios y de los Administradores para poder tener períodos de poco trabajo, poca facturación y/o poco crecimiento de la empresa, para que pague los gastos y yo me dedique a buscar dinero en otro lado.
- FISCAL / IMPOSITIVA (Si alguna política fiscal de Argentina nos limita o prohibe trabajar)
  - Ya tenemos registrada la empresa en USA con una LLC. La idea es llevar la mayoría de la facturación a ese país para escapar de los peligros de Argentina.
