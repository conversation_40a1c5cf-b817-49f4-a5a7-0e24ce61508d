# CAPACITACIONES


Chips de otras carreras, ubicación antenas, control de puesto real

## CURSOS

- Informe de largadas: https://cronometrajeinstantaneo.com/resultados/32-rally-rac-1000/largadas?control=PQAC

## TEXTOS VENTAS

El sistema cuenta con un widgets que generan información para utilizar en softwares de streaming para TV y redes sociales (OBS Studio, vMix, Wirecast, Streamlabs OSB, etc.):

- <PERSON><PERSON> (últimos participantes en llegada)
- Participantes en pista
- Participante con Reloj
- Sólo Reloj
- Sólo Participante
- Podio (principal o por categoría)

Trabajamos junto con el equipo de diseño para adaptar la identidad de los layouts, ya que podemos adaptar el diseño según la necesidad.

Algunos ejemplos de eventos anteriores son:

https://www.youtube.com/live/sjADfxyLfP0
https://www.youtube.com/live/fmUpiYIEMI0
https://fb.watch/nHOdti1bJz/?mibextid=Nif5oz


## VIVO EN INSTAGRAM

- Hacer listado de preguntas
- Probar invitación antes
- Usar mic para que se escuche mejor
- Sacar foto mientras hago vivo

## FEEDBACK DE USHUAIA PARA ORDENAR CRONOMETRAJE CON CHIPS

- En acreditaciones se cruzaron chips
- No pudimos trabajar viendo la computadora de chips por lo que no sabíamos cuáles pasaban sin leer o con lectura cruzada
- No pudimos usar las cámaras, la TV llegó muy tarde, no pudimos estar con el TV y la gente tapaba las cámaras
- El backup con app fue muy difícil porque se cruzaba mucha gente
- No tuvimos la información de los DNF ordenada, la fuimos subiendo tarde y como pudimos
- La app de Griselda se sincronizó durante la entrega de premios, faltaban muchos tiempos y tenía muchos errores
- Tendría que haber ido con nube híbrida por la falla de Internet

- Tener un plano del parque cerrado, arco y lugar para cronometradores con tiempo y exigir que se cumpla
- Parque cerrado sin participantes o sector privado para cronometraje cuidando cables, cámaras, backup con apps, y gazebo
- CCTV con TV propia y cuidada
- Persona 100m antes avisando a los que no se les ve el número
- En las acreditaciones se cruzaron chips. Lo ideal es asignarlos antes y sólo probarlos durante la acreditación. También sería bueno tener un kiosko a parte para auto-chequeo
- La computadora con los chips y las cámaras es trabajo de una persona viendo si se escapan
- Acordar comunicación de DNFs por un sólo canal y con un sólo respondable

- No aceptar armado sin tiempo
- No permitir excepciones, es poco profesional y tu laburo sale mal
- En eventos medianos y grandes, el responsable no puede estar cronometrando, sino pendiente de todo



-------
Si, buena pregunta, tendría que haberlo explicado. Cuando pasas un chip ya asignado y hay un número ingresado, el sistema re-asigna el chip


---

En responsable de cronometraje:
- Todo lo que te pasó en Ushuaia
- Listado de personas que van a cronometrar
- Listado de materiales
- Conocer el reglamento completo
- BackUps con cámara
- Manejo del Modificador de tiempos
- Coordinar prueba con los operadores
- Coordinar con el responsable de acreditaciones

Para tener en cuenta con RFID y eventos grandes:
- El Control de chips en largadas permite detectar participantes con cambios de distancias sin informar
- Agregar los CONTROLAR XXX en los resultados antes de entregar los podios
- Es ideal que los kits con sus números y chips ya estén en las bolsas listas antes de las acreditaciones
- El control de chips es obligatorio, idealmente con un kiosko y una persona para los cambios


## CURSO CRONO + 1000 RUNNING

Me falta escribir:

- Acuerdo con el organizador (contrato de cronometraje)
- Puestos de control y horario de corte
- Mostrar resultados instantaneos
- Cooperar con locutor y fotógrafos
- Backup manual y/o segunda línea
- Backup con vídeo
- Cronograma de armado, desarme y descanso
- Control de materiales, cables y prueba de chips
- Control de cronometraje y armado de podio (chips, repetidos, ignorar)
- Estar en el podio
- Control de devolución de chips
- Puntos, ITRA, UTMB, velocidad promedio, etc.




Les paso un resumen a modo de checklist de los temas a tener en cuenta para un running de más de 1000 participantes

*Presupuesto y profesionalidad*

Desde el comienzo es importante planear un servicio profesional en todo sentido y hay que tener en cuenta todo lo necesario dentro del presupuesto.

*Inscripciones y asignación de chips*

Hay que llegar a un acuerdo con el organizador de cómo se van a registrar las inscripciones, poniendo énfasis en que como máximo 48hs antes del evento ya esté todo migrado al sistema y no se utilicen otros medios de inscripciones o carga de datos por fuera.

La asignación y control de chips debe estar resuelto también 48hs antes.

Lady se compromete a ver los vídeos del manejo de excel y a probar distintos tipos de importaciones y asignación de chips con el lector USB:

- Para la importación y manejo de Excel: https://cronometrajeinstantaneo.com/cursos/importar-exportar-participantes/
- Para la asignación manual de chips: https://cronometrajeinstantaneo.com/cursos/cronometraje-con-chips/

Además van a publicar en todos los medios posibles (web, redes sociales, qr, mails, etc.) el listado de participantes final para que los mismos participantes puedan comprobar que sus datos estén bien.

*Acreditaciones*

Hay que tener planeada la acreditación para que no se generen filas y haya tiempo disponible para corregir los que estén mal categorizados. Hay muchas variables para ver, pero hay que pensarlo con tiempo y tener la gente y equipos necesarios para que fluyan como corresponde.

Lady se compromete a probar las apps de acreditaciones y a analizar la acreditación del próximo evento.

*Diseño de parque cerrado, conexiones, electrcidad*

De antemano debe estar acordado un croquis como va a ser la llegada, priorizando que no haya público, ni gente, ni nadie cerca de las antenas, que los operadores de la app para backup manual estén libres y que las cámaras que filman la llegada no sean obstruidas.

El control de la señal de internet y de la electrcidad debe estar hecho por lo menos 24hs antes del evento para poder solventar problemas de conexión.

Hay que tener un listado de todos los cables necesarios, más cables y equipos de repuesto.

Lady se compromete a que estos cables y equipos se deben probar durante la semana previa al evento y una vez que ya está todo armado el día del evento. Hay que reservar unos 30 minutos en el cronograma del armado para poder realizar estas pruebas y cambiar algún cable que pueda fallar.

*Responsable de cronometraje libre*

Es importante que el responsable de cronometraje, en nuestro caso Lady, esté libre para poder ir verificando que todos los equipos estén funcionando, que los informes estén correctos, detectando errores y con tiempo para corregirlos. También es importante que tenga tiempo para poder atender reclamos de participantes y consultas de la organización.

*Backup*

Lo ideal es tener un backup para analizar y recuperar información rápida, que casi siempre usamos cronometraje con nuestra app. Además hay que tener un backup en vídeo que demora más el control pero nos puede permitir buscar y ver la llegada completa.

*Evento de prueba*

Para practicar todo un caso real, este fin de semana van a organizar un evento de prueba. Nosotros nos comprometemos a estar online para poder ir resolviendo todas las dudas que vayan surgiendo.