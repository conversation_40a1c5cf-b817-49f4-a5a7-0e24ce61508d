## DESCRIPCIÓN

Nuestra empresa brinda servicios de gestión de participantes, cronometraje y resultados en eventos deportivos. Tenemos un uso moderado de lunes a viernes, principalmente de configuración de eventos y de inscripciones de participantes. Los fines de semana, tenemos un uso intensivo de cronometraje y resultados. Trabajamos en todo América Latina en varios deportes diferentes, entre ellos hay muchos puntos de trabajo offline con nuestras aplicaciones que luego se van sincronizando.

Los sábados y principalmnete los domingos, tenemos varios miles de tomas de tiempo en nuestras aplicaciones desde distintos países, que llegan a nuestra API y se guardan en una tabla de MySQL. Luego son procesados por queues para generar los tiempos y los resultados.

El problema que estamos teniendo es que la base de datos y el servidor se satura y los resultados se demoran. Por eso, queremos migrar a una arquitectura serverless para poder escalar automáticamente y no tener problemas de saturación.

Somos una empresa muy chica y soy el único desarrollador. Por eso, necesito la ayuda de ustedes en seleccionar las tecnologías correctas, ya que no puedo darme el lujo de equivocarme, ni de hacer pruebas en distintas tecnologías.

## SERVIDOR ACTUAL

Hoy tengo una instancia en Google Cloud Engine con un clásico LAMP (Ubuntu + PHP 8.3 + Mysql + Apache), y uso mucho el framework Laravel. Para todas nuestras herramientas de gestión, este servidor es suficiente, funciona perfecto y no tengo intensiones de cambiar la arquitectura. Pero para los tiempos y los resultados, no lo es.

## IDEAS

Estas son algunas de las ideas que estoy planeando:

*Cloud MySQL*: para los datos relacionados. Si bien sería lo lógico, vimos que no me da un beneficio significativo y si me eleva muchísimo el costo. Por el momento quiero mantener el Mysql en nuestra instancia y que se pueda conectar desde Cloud Functions.

*Firestore Realtime Database*: Si pasamos las tablas de tomas de tiempos y resultados a esta base de datos, podríamos modificar nuestras aplicaciones para que la sincronización sea más automática (hoy es todo código propio). Luego los procesos de colas y generación de resultados se podrían hacer con Cloud Functions. Para esto tendríamos mucho trabajo en duplicar los datos entre las bases de datos para no perder la historia e incluso poder hacerlo de a poco.

*Cloud Functions*: para los scripts, queues y procesos varios. Hoy están en queues de Laravel, pero si los paso a Cloud Functions, podría escalar automáticamente y no tener problemas de saturación. Utilizo en otro proyecto Lambda en AWS y los costos son muy lógicos y accesibles. Supongo que en este caso será lo mismo.

*Cloud CDN*: otra opción que estuve viendo es poder seguir trabajando con los queues de Laravel, pero los resultados subirlos a Cloud CDN y de ahí programar para que los resultados se actualicen en las apps desde ahí. Parece ser más fácil para desarrollar pero no se si será suficientemente rápido, ni los costos que puede traer.

*Cloud Storage*: para los datos estáticos web, calendarios y eventos (micro sitios). Hoy generamos archivos html que se guardan en archivos de la instancia. Creo que si estos archivos los genera un CLoud Function y lo sube directamente a Cloud Storage, sería mucho más escalable.

*Cloud Run*: más adelante para poder pasar todo a serverless, puede ser una buena opción migrar mi Laravel a este servicio. Pero por el momento no tengo intenciones de hacerlo por el costo que puede traer. ¿Creen que va a ser costoso mantener un Cloud Run con un Laravel? Hoy el costo mensual de mi instancia es muy bajo, menos de 100 dólares y cubre perfectamente lo que necesito.