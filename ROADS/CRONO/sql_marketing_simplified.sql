-- 1. <PERSON><PERSON>r tabla de tracking de marketing (simplificada)
CREATE TABLE marketing_tracking (
    idtracking INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    idevento INT UNSIGNED NOT NULL,
    idinscripcion INT UNSIGNED NULL,
    utm_source VARCHAR(100) NULL,
    utm_medium VARCHAR(100) NULL,
    utm_campaign VARCHAR(100) NULL,
    conversion_step ENUM('event_visit', 'form_complete', 'payment_complete') NOT NULL,
    conversion_value DECIMAL(10,2) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_evento (idevento),
    INDEX idx_inscripcion (idinscripcion),
    INDEX idx_conversion (conversion_step),
    FOREIGN KEY (idevento) REFERENCES eventos(idevento) ON DELETE CASCADE,
    FOREIGN KEY (idinscripcion) REFERENCES participantes(idinscripcion) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Crear tabla de pixels de marketing (simplificada)
CREATE TABLE marketing_pixels (
    idpixel INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    idevento INT UNSIGNED NOT NULL,
    pixel_type ENUM('google_analytics', 'meta_pixel', 'custom') NOT NULL,
    pixel_id VARCHAR(255) NOT NULL,
    pixel_name VARCHAR(100) NOT NULL,
    track_event_visit TINYINT(1) DEFAULT 1,
    track_form_complete TINYINT(1) DEFAULT 1,
    track_payment_complete TINYINT(1) DEFAULT 1,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_evento (idevento),
    INDEX idx_type (pixel_type),
    INDEX idx_active (is_active),
    FOREIGN KEY (idevento) REFERENCES eventos(idevento) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Agregar campo marketing a la tabla eventos (después de vivo)
ALTER TABLE eventos 
ADD COLUMN marketing TINYINT(1) DEFAULT 0 AFTER vivo;

-- 4. Insertar el pixel de Meta (solo con ID)
INSERT INTO marketing_pixels (idevento, pixel_type, pixel_id, pixel_name, track_event_visit, track_form_complete, track_payment_complete) VALUES
(1, 'meta_pixel', '729777596302705', 'Meta Pixel', 1, 1, 1);

-- 5. Habilitar marketing para un evento de prueba
UPDATE eventos SET marketing = 1 WHERE idevento = 1; 