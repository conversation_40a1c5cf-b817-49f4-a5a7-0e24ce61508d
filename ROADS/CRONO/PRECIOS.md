# MODELO DE PRECIOS CRONO

## DISTINTOS PRECIOS

Vamos a manejar 3 precios diferentes de servicios para nuestros 3 tipos de clientes:

- **Auto-cronometraje**: U$D 120 por evento, con acceso a todas las funcionalidades y con soporte completo que incluye hacerle todas las configuraciones necesarias.
- **Cronometrador**: U$D 80 por evento, con acceso a todas las funcionalidades de la plataforma. No se le hacen configuraciones, solo se le explica el uso de la plataforma.
- **Organizador**: U$D 60 por evento, con acceso solo a las funcionalidades de inscripciones.
- **Sitio web**: es gratis y todo el mundo puede generarse un usuario y configurar un evento para esta funcionalidad.

## PROMOCIONES

Tenemos las siguientes promociones:

- 2x3: paga 2 eventos al valor según su tipo de cliente y obtiene un 3ro gratis. SÓLO PARA EVENTOS FUTUROS.
- 6x10: paga 6 eventos al valor según su tipo de cliente y obtiene 10 eventos. SÓLO PARA EVENTOS FUTUROS.
- Cripto: por el pago en USDT o BTC obtiene un U$D 10 de descuento. Este descuento solo lo comentamos en casos especiales.

## ACLARACIONES

- No vamos a permitir el pago parcial de los eventos, pero si que puedas pagar el módulo de inscripciones y después el de cronometraje. Vamos a habilitarlos según el pago.
- No vamos a permitir el módulo de cronometraje sin el de inscripciones.
- La idea es empezar a automatizar la gestión de eventos y los pagos. Por este motivo se van a poder generar los usuarios primero ellos mismos y sin nuestra intervención. Después vamos a habilitar a mano el permiso de Organizador y el de Cronometrador, que le cambiará los módulos a los que tiene acceso y los valores del sistema que se le muestran.
- Los pocos clientes con descuentos especiales se terminan y pasan a este nuevo sistema.
- Vamos a poder asignar descuentos puntuales por evento, pero no por usuario o cronometrador. Cada descuento se aprueba y se carga a mano.
- Vamos a permitir distintas cantidades de eventos pendientes futuros, pero no eventos pasados. El control de eventos pasados se va a hacer con un aviso automático por mail los días lunes y por un botón para bloquear al usuario que lo activaremos manualmente, luego de que Juli se contacte con ellos.