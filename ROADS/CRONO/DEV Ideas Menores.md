# 🥇 CRONO > DEV Ideas Menores

## ORDENAR:

- En la app y en la api agregar compatibilidad para chips numerados sin tener en cuenta los PCs
- Ver si combiene cambiar organización por empresa y incluir otros tipos: streaming, medallas, locutor, etc.
- Los puestos de control pueden tener condiciones de exclusión: antes/ después de una hora, antes/después de un puesto, diferencia de tiempo entre otro puesto, etc. Puede haber una configuración si se excluye y/o si se genera alerta de revisión
- Agregar un tipo de control backup "BackUp Llegada", para que si no se lee el chip en el control principal, se lea en el backup pero y genere una alerta si se especificó
- En México le dicen Folio a la Ficha de inscripción
- Agregar seguimiento a utm_source y utm_campaign
- Inventar algo copado para los talles de las remeras
- Y se puede que al completar el tiempo de 1.000m que le pido solo puedan poner en formato mm:ss?? Porque todos ponen diferente y después me vuelvo loco armando los grupos.. (Pedida por OWE)
- Tiempo promedio de la carrera, sexo y categoría entre todos los participantes que llegaron para comparar en el ticket
- Agregar main sponsor y colaboradores con distintos tamaños y apariciones
- Agregar género a la importación de participantes, exportación y los informes
- Agregar horas de vencimiento de inscripción para hacer como las reservas de alojamiento con respecto al pago
- Agregar modificación del texto de datos extras así se pueden re-usar muchos.
- Si es así sugiero q en la tabla la dupla la cuente como 2 personas y no como una, así da el número real de participantes
- Aplicar fecha de cerrado (con mi revisión) y ahí no dejar resetear, ni modificar fecha, ni borrar participantes. Alertar al re-abrir evento.
- Podios generales arriba de podios por categorías
- Separar falsa partida y redondeo para que se pueda redondear siempre
- En el nuevo modificador de tiempos, poder "Agregar antes de x participante"
- Mejorar fuenta para las columnas de tiempos en los resultados (la del reloj de la app puede servir)
- Columna whatsapp en seguridad en lugar de seguridad
- Opción para cobrar al cambiar la distancia o cualquier cosa (ver que se puede cambiar)
- Kit a la carta: que puedas seleccionar combos de distintas opciones y que eso incluya o no productos que tienen que tener un stock por talle
- Agregar la opción de filtros como selectores
- El informe de estadísticas de datos extras no tiene en cuenta los participantes de equipos y por ende no aparecen sus remeras
- Banderas en el Vivo
- Funcionalidad para generar sorteos
- Acreditación por correo o QR
- Mostrar la edad real al lado de la fecha de nacimiento en inscripciones y panel de usuario
- Pasar los mails de usuarios siempre a minúscula
- Podemos usar https://pickit.com.ar para la entrega de kits por correo
- En equipos poder llamar al tipo de participante (acompañante, bici, etc.)
- Copiar https://www.formassembly.com/
- Agregar posibilidad de ingresar sus propios píxeles de Facebook o Google a los organizadores
- Ajax loading del loguito de crono
- Lindo diseño en https://wirebox.app
- Datos extras del participante abajo del nombre
- Diferencia primero y anterior abajo del tiempo total (ver PDF de Colombia)
- Pasar las capacitaciones al sistema
- Poder exportar las estadísticas de la app con chips y/o compararlo online (herramienta para calcular el % de lectura)
- El cancelar la modificación del participante se debería poder aunque haya datos obligatorios incompletos
- Resaltar repetidos en Informe PC
- Resaltar los repetidos dentro de la app (puede ser opción en los filtros)
- Revisar en el vivo que si una largada se pone mal el número, al corregirlo no se cambia
- Agregar un tipo de dato check sin pregunta (para el tema del deslinde y otros revisar)
- Sacar uuid de lo que no hace falta
- Agregar 120 y 300 segundos en el cronómetro
- Buscador de localidad desde una API así estandarizamos
- El informe de seguridad debería estar en un panel del evento: cuantos hay, falta, etc. cuántos podios están completos (tener en cuenta que puede haber menos gente en algunas categorías)
- Agregar opciones para cobrar por Whatsapp: https://onepay.ai/
- El reloj en la app con múltiples carreras
- La app de Windows debería poder re-dimensionarse
- Estaría bueno poder generar scripts y dejarlos pre-cargados para ser ejecutados, para por ej lo del nacional de Enduro
- Pruebas de pagos con MP https://groups.google.com/g/mercadopago-developers/c/oU0KdDPYu2Q/m/XY8-EdDmBwAJ?utm_medium=email&utm_source=footer
- Agregar código QR para ver resultados y para inscripciones
- Pasar localidad como dato extra
- Varias ideas de https://results.sporthive.com/events/7042627784530419712/races/2/bib/11 : Íconos para las disciplinas en las etapas, Pos gen, cat y sexo en cada etapa y con el total de participantes, Gráfico de velocidad promedio de todos los participantes comparado con cada uno, Finish overview
- Lindo diseño de streaming para replicar: https://youtu.be/8O8gr3OF1ug?t=170 / https://youtu.be/8O8gr3OF1ug?t=2163
- Para copiar en las estadísticas de la app (ver también si sirve usar svg o )
  https://codecanyon.net/item/sticky-mobile-phonegap-cordova-mobile-app/********
  https://codecanyon.net/item/banking-one-mobile-app-template-ionic-6-capacitor-3/********
  https://codecanyon.net/item/zekky-ionic-4-angular-7-ui-theme-template-app-multipurpose-starter-app/screenshots/********?_ga=2.********.*********.**********-**********.**********
- Mostrar mail del equipo en el Panel de Control (ver que se mande mails a ambos)
- Me sirve agregar el país a la generación de eventos (sacarlo de las organizaciones)
- Separar el tema del admin a un services
- La tabla contacts tendría que tener también eventos
- Constant FILTER_SANITIZE_STRING is deprecated in /var/www/cronometrajeinstantaneo
- Mira como el nombre está grande y ocupa 2 renglones y como queda el título arriba: https://runsignup.com/Race/Results/146424/#resultSetId-384367;perpage:100
- Para el GPS https://html5.anube.es/?rally=rally4465&port=auto&token=boBV923HSI&map=satellite&type=animation&participants=251&participant_ids=1690596&from=**********&to=**********
- WhatsApp flotando con el número de teléfono del organizador
- Agregar más de un mail a las notificaciones del sistema. Ya sea con coma, o a modo de suscripción del cronometrador
- Open source software para NVR en frigate.video
- Agregar copiar enlace en las opciones para descargar y en la parte de widgets
- Contador en la misma APP
- Tengo en Sendgrid
- Ver si sumamos seguros con Andres de Mendoza
- Armar partidas desde informe de categorías alreves
- https://marketing4ecommerce.net/herramientas-para-eventos-virtuales-con-las-que-sorprender-a-tu-publico/
- Separar la activación de apps de las largadas
- Porer pasar todos los parámetros del streaming por parámetro, especialmente el diff
- Acomodar los títulos con colspan
- Ver si podemos poner parciales como un selector más
- Agregar la opción de filtros como selectores
- Bloquear eventos terminados (no se puede resetear, ni modificar el nombre, ni fecha, ni nada masivo)
- Revisar Meet Manager para natación https://www.youtube.com/watch?v=oM_3VyXTOds
- Ver de copiar ideas y diseño de https://www.redbullxalps.com/
- Tenés razón, hoy dice "ERROR: no se encontró un participante" pero tendría que salir algo mucho más amigable.
- Ver si se puede Cambiar la ip del reader para tener 2 conectados a una misma app (o 2 pcs pero en la misma red)
- Ver si se puede tener 2 apps para ver si podemos tener 2 readers con la misma PC o celu
- Opcion para mostrar banderas o nombre de pais
- Mover masivamente de categoría (en el panel de control con Livewire)
- Terminar lo de pre-llegada en la meta como opcion
- Agregar más estados a los eventos: pendiente, terminado, cancelado, suspendido, postergado (sin fecha)
- Poder pasar el pago a otro evento
- Cartel de eventos terminados para que lo cierren
- El streaming tiene que funcionar también con los cambios de etapa
- Tiempos a revisión (analizar la velocidad promedio por etapa dependiendo de la disciplina)
- Ver si agregamos una aprobación de que el evento terminó bien por el cronometrador y otro por nosotros
- Tiempos de Vueltas en un segundo renglón (https://www.endurofim.cl/wp-content/uploads/2021/08/Official-Classification-S1-2.pdf)
- Informe de Datos extras con equipos con un equipo por cada línea
- Preparar el vivo del locutor para que funcione en tablets y celulares como "App para locutor/narrador"
- Fichas y pago con teclado
- El modificador de tiempos sin mostrar hasta que se apliquen filtros (buscar y ordenar en un issue, sumarle los eliminados y poder modificar en masa)
- Agregar diferencia de tiempo en streaming para no tener que configurar la hora de la PC del streamer
- Agregar el nombre del evento en el título
- En la sección filtro nos lleve directamente a las categorías (en usuario Worktime Chile, avisar)
- Si en consulta de ticket pones número de participante pero hay algo en el buscador, hace la búsqueda igual sin prestar atención al botón que se apretó
- Múltiples categorías
- Filtra tiempos y participantes por GET (si vas atrás pide re-enviar)
- Botón para bloquear y liberar los códigos de controles (pasan todos a 0000) y no mostrar los códigos 0000 sino como Liberado
- Abreviación para las categorías
- En el modificador de tiempos poder deshabilitar todo un cronometrador
- Informe para exportar a excel como lo pide ITRA o UTMB
- Revisar https://github.com/don/cordova-plugin-ble-central/issues/932#issuecomment-1307057842
- Al agregar una hora de inicio y fin de cada carrera, podemos filtrar los chips que se leen antes y después y no computarlas (sería ideal meterlo junto con el tipo de largada en carreras)
- both VLC and OBS and had OBS capture the webcam stream ... then I pointed VLC at the stream file on the hard drive
- First, for the SMS results. Could you not send them to people who are marked DNS?
- https://code-boxx.com/generate-qr-code-javascript/
- Que se pueda leer QRs que sólo tengan el número del participante y QRs que tengan el ticket digital
- Al exportar, que también se pueda bajar en formato texto y html para la prensa
- Comando para activar streaming que tenga en cuenta el tipo de largada y que exista config_lives
- Recuperar el hash en los js de admin
- Volver a ver el laravel.log y warnings
- La App en las playstore debería funcionarnos como otro punto de venta (explicar con algún vídeo, mandar a redes, dejar probar la app de alguna forma)
- Control de que no se pueda guardar lo de un participante en otro evento si se cambia por el historial en otra pestaña
- Cambiar localidad por provincia, equipo, etc.
- Generar estadísticas desde logs
- Constant FILTER_SANITIZE_STRING is deprecated to FILTER_SANITIZE_FULL_SPECIAL_CHARS
- Optional parameter $view declared before required parameter $request is implicitly treated as a required parameter in /var/www/cronometrajeinstantaneo/admin/app/Http/Controllers/OldController.php on line 15
- No calcular nunca con tiempo de largada en cero
- Tengo algunas cosas para acomodar en el cuaderno que ya lo puedo vaciar
- Información de nueva API WhatsApp https://www.facebookblueprint.com/student/collection/409587
- Probar con mejores scanners ( https://github.com/bitpay/cordova-plugin-qrscanner )
- Mejorar más todavía la forma de mostrar errores en pantalla. Pudiendo mostrar mensajes desde el through y con un diseño más lindo
- Compartir resultados a Whatsapp, XLS y PDF con una linda barra
- En la funcion convertir_final se pueden detectar tiempos negativos y muy grandes para avisar de errores
- Para chusmear: https://www.fotomotor.es/competicion/2038/tramo/11/ y https://www.instagram.com/p/CLKxrQCng2d/?igshid=YmMyMTA2M2Y%3D
- App para largador con participante y tiempo de descuento
- Vídeos que muestran como configurar pantallas para mostrar resultados: https://www.youtube.com/watch?v=WYkPKiEepwA&list=PLMTIVfFlMEkUEN-5WHqfD-Hkcmcm-VPPt
- Tiene que haber un demo para cada deporte y que no se pueda modificar nada de la configuración
- Vídeos dentro del sistema, por ejemplo uno cuando recién se registra y otro cuando paga agradeciendo
- Usuario Responsable de cronometraje en el sistema
- https://imperavi.com/revolvapp/
- Hay varias ideas para copiar de Rufus Cloud: https://mail.google.com/mail/u/0/#inbox/********************************
- Ordenar en el modificador de tiempos las etapas (poner nombre de la carrera en la etapa)
- Poder tomar tener tiempo de chip y tiempo de carrera en el ticket y resultados
- Totales arriba en el inicio. Si hay más de 500 agregar filtros por carreras y avisar
- Preparar que se pueda tener el sistema sólo para acreditaciones
- En la nueva ventana de eventos: Resaltar el próximo, marcar con algo flotante y visible el que es HOY y el que es Esta semana (agregar fecha de inicio y fin del evento o si es evento de un sólo día, también tener en cuenta si ya lo dieron por terminado)
- Audio diciendo quien llegó y su posición y tiempo (que se pueda poner al vídeo también)
- Sistema de puntos con ecuaciones. Puede haber un dato extra (puntos anteriores) y que se sume como puntaje total. También se podría ordenar por puntos o tener un informe específico de orden por puntos
- Separar la configuración de eventos (renombrar a js y css)
- Avisarme si alguien carga js personalizado
- Pasar los mensajes a notificaciones
- Cambiar las autenticaciones a la API por Laravel Sanctum https://laravel.com/docs/8.x/sanctum
- Resultados compartir en instagram o Facebook (también en el ticket)
- Ver ideas en https://www.instagram.com/p/CbERImjsI7j/?igshid=MDJmNzVkMjY=
- Laravel 9
- Evaluar https://ably.com/
- Que en las inscripciones se muestre un desplegable de países según la tabla y que reconozca el del navegador o el de la IP
- Presentar como quiere la UCI
  https://www.pinkbike.com/news/final-results-from-the-petrpolis-xc-world-cup-2022.html
  https://www.uci.org/race-hub/2022-mercedes-benz-uci-mountain-bike-world-cup-xco-xcc-petropolis/2YgV3VbwPiNFsumC53IvuH?tab=xco-women-elite
- Mostrar nacionalidad resumida (ARG), completa o bandera
- https://ayudawp.com/alojar-localmente-fuentes-google-astra/
- Un montón de Livewire components: https://www.youtube.com/watch?v=h1K9IFfPU68 y más en https://livewirekit.com
- utm_source	utm_campaign
- flush(); en penas (por las dudas también en otros listados como modificador de tiempos)
- Ticket sólo de la final
- No mostrar tiempos negativos es mejor que no esté
- Penalizaciones automáticas en FIM
- Reondear a décimas o centécimas (cronometrar con la presición que se va a mostrar) truncado para que sea igual a tarjetas
- Tarjetas/carnet de rally virtuales
- Habilitar etapas para mostrar
- Ver de habilitar firewall y cdn
- https://www.datadoghq.com/dg/apm/laravel-application-performance
- Penas como minutos en negativo se ve feo (sacar la aclaración de seg)
- Impresión de penalizaciones por día
- Seguro de devolución ( https://mail.google.com/mail/u/0/#inbox/******************************** )
- Tiene que llevar los logos las impresiones y se firman como oficial
- Diseño de impresión prolijo y diferente al de resultados
- Lindos gifts, parecen intuitivos Technology and Software Companies - COR https://projectcor.com/technology-and-software-companies/product/
- Para xco que en los listados esté la posición y si sigue o no tipo semaforo
- Tomar varias tomas de un mismo chip o Beacon y generar cual es el correcto (subir online un paquete en json de esas lecturas)
- Mejorar el cartel de error "Sorry, the page ..."
- Configurar ticket digital para que incluya: todo lo necesario para una tarjeta de rally, para eso son los PCs, y que con los nombres de los PCs quede con esa info
- Tipo de largada PC anterior para entrenamiento y eventos de varias pasadas
- Revisar si se arregló el menú en celulares (probar en más)
- Poner sub-menu para los pasos del wizard de configuraciones
- Probar versión actual del sistema en nube híbrida
- Laravel Logs: https://laravel-news.com/laravel-authentication-logs
- Tener un tiempo oficial (Tiempo Bruto o Tiempo de Competencia) y tiempo de chip (Tiempo Neto o Tiempo Real).
- Me gustaría cambiar el campo tiempo por crono en la tabla lecturas y en todo el código de app y admin
- Poder mover cosas (por ejemplo categoría a otra carrera)
- Revisar logs de apache en prod
- Agregar verificación en la inscripción cruzando la fecha de nacimiento y la categoría (puede ir muy bien con Vue.js)
- Pero hay que incorporar a los descalificados y fondo de la misma tabla de posiciones
- Probar en otras máquinas y comparar tiempos (NTP)
- 404 page
- El campo eventos.equipo lo uso para el informe de extras (puede que en otros lados más) pero se puede matar
- Quiere desafiar a otro participante
- Hacer certificados
- Subir fotos de los corredores
- https://tiargentina.com/empresa-relet-srl-distribuidor-de-networking-em-palermo-1285 (Relet SRL Distribuidor de Networking)
- App para mostrar tiempos y seguimientos (con hot sit)
- Foto Finish
- DietPi, la distribución Linux ultraligera para Raspberry Pi (y otra media docena de SBCs)… que también puedes instalar en tu PC "DietPi, la distribución Linux ultraligera para Raspberry Pi (y otra media docena de SBCs)… que también puedes instalar en tu PC" [DietPi, la distribución Linux ultraligera para Raspberry Pi (y otra media docena de SBCs)… que también puedes instalar en tu PC](https://www.genbeta.com/linux/dietpi-distribucion-linux-ultraligera-para-raspberry-pi-otra-media-docena-sbcs-que-tambien-puedes-instalar-tu-pc/amp)
- Listado de actividad con posibilidad de deshacer
- Datos de clima u otros factores externos
- https://www.2checkout.com/payment-api
- Agregar como estado de pago a liberado (también puede ser pendiente, pago a medias, saldo, etc.)
- Bajar la importancia de localidad y ponerlo como dato extra
- Googlear mucho y buscar también en Youtube distintas formas de cronometrar o fotocélulas
- BORRASTE AAVLA, ¿Cómo hacer que no pase nunca más? => Terminar sincronización nube y sólo usar nube, nunca tu compu si no es beta de funcionalidad
- En generales y ticket si las categorías tienen el mismo nombre las suma aunque sean diferentes (https://cronometrajeinstantaneo.com/resultados/desafio-del-lago-2021/generales)
- Darle tiempo a la metralladora de tiempos
- Cambiar la palabra sexo por género y agregar otro
- PayU ver comisiones e implementación porque no es como ML
- Para running quieren simplificar las cobranzas como objeción
- Agregar registro del móvil a los logs para revisar posibles fraudes (lo pidieron en México)
- Estaría bueno que las remeras del evento tengan control de stock
- Cache bien hecho en Laravel: https://www.youtube.com/watch?v=Nmq3lmgi_Sk | https://twitter.com/themsaid/status/1369307734433275917?s=09
- No dejar repetir nombre de categoría en una misma carrera
- Versión para iPhone (https://volt.build)
- Control de paso y alerta para saber cuándo es la última vuelta
- Redondear largada para arriba a minuto cerrado o para abajo
- Poder modificar el codigo de un evento o que no cambie cuando le cambias el nombre
- Algo para ayudar a cargar las fechas desde y hasta
- Los pasos del wizard no se entienden como botones
- Laravel Vapor es Laravel común hecho serverless
- Mail igual que el de Ride que es hermoso (https://mail.google.com/mail/u/0?ik=8d55b10a2d&view=om&permmsgid=msg-f%3A1696836627049194712)
- Sacar la suscripción al newsletter O USARLA (pero igual ponerla más abajo)
- Terminar diseño y agregar enlaces a Condiciones y Privacidad
- Cerrar la funcionalidad de módulo médico y de seguridad (poner usuario o QR para habilitar y también anunciar temas médicos y que puedan reportar cosas)
- The ideal solution would be to fix all the data in the database. That is, go through your database and update your datetime/timestamp fields so that they are nullable, and convert their data from '0000-00-00 00:00:00' to null.
- Mensajes de respuestas en blade que sean flotantes
- Trabajar con los participantes como si fuera un Excel (tiene que haber algún plugin para Laravel)
- Cronotech cobra $32500 hasta 150 personas y de ahí $250 más por corredor, Claudio quizo sobrar $25000 menos lo mío le quedaría sólo $10000, hay que ver esa ecuación. A Damian le parecía caro y por eso no lo usaba.
- Ver por productos con Lora en https://www.inipop.com/static_page/
- Agregar orden interno a todo (modelo y tablas hoy)
- Firma electrónica de Adobe
- Ya están los campos orden en varias tablas
- Revisar que por ahora sólo calcula cuando existe un idparticipante
- Composer install --no-dev y bajar debug a true en produccion
- Ver este video para ordenar el código: https://www.youtube.com/watch?v=ShrS3HXwzPg
- Ver estas opciones para queues: https://github.com/php-enqueue/enqueue-dev y https://cloud.google.com/pubsub
- Cómo quitar el JavaScript para responder comentarios en WordPress si no lo necesitas: https://ayudawp.com/quitar-javascript-comentarios-wordpress/
- Opciones para compartir fácilmente el ticket digital en redes sociales
- Agregar opción para desactivar un punto de control completo en el crono (para usar tiempos temporales de largada por ejemplo)
- Mail al finalizar el evento
- En SaaS el script no está marcando el generar el movimiento de saldo en las ventas
- En la nube necesito saber que versión tengo
- Necesito probar una actualización de la nube con algún usuario antes de la carrera
- Hermoso diseño, campeonato e íconos https://xipgroc.cat/ca/lliga/copa-marnaton-2019
- Informe por orden de llegada, sin importar categorías ni carreras
- Para mejorar en subir
- Agregar validacion de tipo al subir
- Agregar compatibilidad con gif
- Pasar diseño a Blade y Bootstrap
- Para la academia se puede copiar la de timesense: https://timingsense.com/academy/
- Cambiar slider en la web por una imágen estática con franjas
- Se pueden hacer muchas más estadísticas
- Gráfico con tiempos en los que se llena el pódio por categoría
- Cantidad de llegadas por minuto por carrera
- Evolución de inscriptos año a año (para eso hay que agrupar las carreras)
- https://public.tableau.com/profile/matiaspatinomayer#!/vizhome/TriatlnEscapeIslaHuemul2020/EscapeIslaHuemulSponsors
- Que se pueda configurar cuántos se quieren en el podio
- Agregar más datos (incluyendo nacionalidad) al ticket
- Pensar como controlar carrera de regularidad
