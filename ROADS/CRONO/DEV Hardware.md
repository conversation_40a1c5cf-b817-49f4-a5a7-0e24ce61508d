
## CRONOMETRADOR

@later
- [ ] Oficina en Van
- [ ] Armar totem con monitor y caja estanco (que se pueda usar para embudo de salida)
- [ ] Batería en la valija del reader (ver que ya está en YouTube lo del yanki)
- [ ] Dejar truss listo con uniones en caja
- [ ] Agregar Acrílico y Led al totem
- [ ] Armar o comprar valijas apilables para 100 chips recuperables
- [ ] Sumar el reader Zebra
- [ ] Comprar 4 antenas de 9db y armar una valija para llevarlas con cables y soportes
- [ ] Imprimir lonas para el gazebo tipo media pared y para cerrar con trípodes ciertos sectores. Tiene que tener ojales para atarlo en varios lados
- [ ] Comprar sillas más chicas para crono
- [ ] Comprar reloj ALGE
- [ ] Comprar fotocélula ALGE
- [ ] Comprar carpa para el auto en Amazon
- [ ] Hacerme el 270° awning

---

### Info varias
- El usuario predeterminado de Zebra es **admin** y la contraseña **change** (no pude probarlo todavía)
- El host predeterminado es FX9600F23B74
- La IP de la placa del Rasberry es ***********
- Adrenaline usa Classify https://www.youtube.com/watch?v=a1FclmzeGdA

### Integraciones
- Integración simple:
	- [ ] Configurar FPT y crear cron + queue para procesar los CSV subidos
- Conectar Zebra directo
	- [ ] Probar conectar directo al reader (No pude, se podría resetear pero puede que no vuelva a funcionar con Macsha)
	- [ ] Buscar la contraseña del Zebra para ver si podemos ver y guardar la configuración (Probar la predeterminada y sino preguntar a Macsha)
	- [ ] Probar de cambiar el reader en el Macsha y ver si funciona
	- [ ] La IP del Zebra puede estar dentro del rango 192.168.2.x (una vez me pude conectar a ************ pero ahora está Macsha ahí)
- Utilizar chips
	- [x] Probar de leer chips con equipos de Gaby (se me ocurre que pueden tener grabado algo los chips)
- Otras pruebas
	- [x] Conectar el Raspberry al tele (Ver si de ahí se pueden ver las conexiones de red y por ende la IP de Zebra. NO se puede acceder)
	- [ ] Conectar las otras antenas
	- [ ] Conseguir un medidor de señal y jugar a ver si sirve

---
## PLACA DE TOQUE
- [ ] Probar de leer desde un Raspberry Pi con el USB
- [ ] Probar con alargue a una PC
- [ ] Probar de conectar a una tablet

---
## KIOSKO
- [ ] Programar una app y/o la misma app para que quede leyendo y mostrando el ticket digital


---
## RELOJ PARTIDA Y LLEGADA
- Reloj en partida (ver tablet) y en llegada en la llegada para el mundial y para Chile
- Tiene que poder mostrar el nombre del próximo que debería salir según listado de partida
- Julio y Oscar de Santiago de Chile estaban buscando eso

