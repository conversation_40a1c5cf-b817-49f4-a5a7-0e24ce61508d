<!DOCTYPE html>
<html>
<head>
    <title>Cronometraje Instantaneo</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" >
    <link rel="stylesheet" href="css/jquery.mobile-1.4.5.min.css" />
    <link rel="stylesheet" href="css/jquery.mobile-crono.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="shortcut icon" href="favicon.ico">


    <!-- Insert this line above script imports  -->
    <script>if (typeof module === 'object') {window.module = module; module = undefined;}</script>

    <!-- Scripts -->
    <script type="text/javascript" src="js/jquery-2.1.1.min.js"></script>
    <script type="text/javascript" src="js/jquery.mobile-1.4.5.min.js"></script>

    <script type="text/javascript" src="js/mustache.js"></script>
    <script type="text/javascript" src="js/helpers.js"></script>
    <script type="text/javascript" src="js/crono.js"></script>
    <script type="text/javascript" src="js/app.js"></script>

    <script type="text/javascript" src="js/hardware.js"></script>
    <script type="text/javascript" src="js/hardwares/invelion.js"></script>
    <script type="text/javascript" src="js/hardwares/chafon.js"></script>
    <script type="text/javascript" src="js/hardwares/reader1.js"></script>
    <script type="text/javascript" src="js/hardwares/reader2.js"></script>
    <script type="text/javascript" src="js/hardwares/reader3.js"></script>
    <script type="text/javascript" src="js/hardwares/chafon_810.js"></script>
    <script type="text/javascript" src="js/hardwares/cronopic1.js"></script>
    <script type="text/javascript" src="js/hardwares/cronopic2.js"></script>
    <script type="text/javascript" src="js/hardwares/cronopic3.js"></script>
    <script type="text/javascript" src="js/hardwares/cronopic4.js"></script>
    <script type="text/javascript" src="js/hardwares/cronopic5.js"></script>
    <script type="text/javascript" src="js/hardwares/cronopic6.js"></script>
    <script type="text/javascript" src="js/hardwares/fotocelula1.js"></script>

    <script type="text/javascript" src="cordova.js"></script>

    <script>
        var enter = true;
        app.initialize();
        hardware.initialize();

    </script>
    <!-- End Scripts -->

</head>

<body>

    <div id="crono-btn">
        <a class="ui-btn" href="#" onclick="app.teclado('crono')" data-icon="check" style="color: #f7931e;" id="crono">
            <img src="images/isotipo.png">
        </a>
    </div>

    <div data-role="footer" data-position="fixed" class="ui-footer ui-bar-a ui-footer-fixed slideup ui-body ui-body-b">
        <button onclick="app.teclado('config')" class="ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-gear">Config</button>
        <button onclick="app.teclado('reenviar')" id="reenviar-btn" class="ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-refresh">Sincro&nbsp;(<span id="reenviar-cantidad"></span>)</button>
    </div>

    <div data-role="page" id="lecturas">
        <div data-role="content">

            <div class="ui-grid-a ui-corner-all" style="font-size: 22px;">
              <div class="ui-block-a" style="text-align: center;"><div class="ui-body ui-bar ui-bar-a text-center" id="reloj">00:00:00</div></div>
              <div class="ui-block-a" style="text-align: center;"><div class="ui-body ui-bar ui-bar-a text-center" id="tiempo_pena">-</div></div>
              <div class="ui-block-b" style="text-align: center;"><div class="ui-body ui-bar ui-bar-a" id="idparticipante" style="color: #f7931e;">-</div></div>
              <div style="display: none;" id="tagID"></div>
            </div>

            <div id="penas" style="text-align: center;"></div>

            <div id="crono-keys">
                <div class="ui-grid-c ui-corner-all">
                    <div class="ui-block-a"><a data-role="button" href="#" onclick="app.teclado('buscador')" data-icon="search" id="buscador-btn"></a></div>
                    <div class="ui-block-b"><a data-role="button" href="#" onclick="app.teclado('1')" alt="1">1</a></div>
                    <div class="ui-block-c"><a data-role="button" href="#" onclick="app.teclado('2')" alt="2">2</a></div>
                    <div class="ui-block-d"><a data-role="button" href="#" onclick="app.teclado('3')" alt="3">3</a></div>
                </div>
                <div class="ui-grid-c ui-corner-all">
                    <div class="ui-block-a"><a data-role="button" href="#" onclick="app.teclado('scanqr')" data-icon="camera" id="qr"></a></div>
                    <div class="ui-block-b"><a data-role="button" href="#" onclick="app.teclado('4')" alt="4">4</a></div>
                    <div class="ui-block-c"><a data-role="button" href="#" onclick="app.teclado('5')" alt="5">5</a></div>
                    <div class="ui-block-d"><a data-role="button" href="#" onclick="app.teclado('6')" alt="6">6</a></div>
                </div>
                <div class="ui-grid-c ui-corner-all">
                    <div class="ui-block-a"><a data-role="button" href="#" onclick="app.teclado('esperar')" data-icon="plus" id="esperar"></a></div>
                    <div class="ui-block-b"><a data-role="button" href="#" onclick="app.teclado('7')" alt="7">7</a></div>
                    <div class="ui-block-c"><a data-role="button" href="#" onclick="app.teclado('8')" alt="8">8</a></div>
                    <div class="ui-block-d"><a data-role="button" href="#" onclick="app.teclado('9')" alt="9">9</a></div>
                </div>
                <div class="ui-grid-c ui-corner-all">
                    <div class="ui-block-a"><a data-role="button" href="#" onclick="app.teclado('multicrono')" data-icon="check" style="height: 25px;" id="multicrono"></a></div>
                    <div class="ui-block-b"><a data-role="button" href="#" onclick="app.teclado('backspace')" data-icon="arrow-l" style="height: 25px;"></a></div>
                    <div class="ui-block-c"><a data-role="button" href="#" onclick="app.teclado('0')" alt="0">0</a></div>
                    <br>
                </div>

                <div id="pena" class="ui-grid-a ui-corner-all">
                    <a data-role="button" href="#" onclick="app.teclado('pena')" data-icon="check" style="color: #f7931e;">PENA</a>
                    <br>
                </div>

                <div id="buscador" class="ui-grid-a ui-corner-all" style="text-align: center; display: none;">
                    <ul id="buscar" data-role="listview" data-inset="true" data-filter="true" data-filter-placeholder="" data-filter-theme="a"></ul>
                    <div id="buscando-gif" style="display: none;"><img src="images/ajax-loader-2.gif"></div>
                    <br>
                </div>

            </div>

            <div id="totales" class="ui-grid-a ui-corner-all"></div>
            <div id="consola"></div>

            <br/>

            <ul data-role="listview" class="ui-listview" id="listaParticipantes"></ul>

            <br/><br/><br/><br/><br/><br/><br/>

        </div>

        <div data-role="popup" id="popup" data-theme="a" class="ui-corner-all"></div>
        <a id="descargarLog" style="display:none"></a>

    </div>

    <!-- Start of second page -->
    <div data-role="page" id="kiosko">
        <iframe src="https://cronometrajeinstantaneo.com/resultados/vuelta-de-obligado-vob-etapa-3-circuito-owa-2324/ticket?idparticipante=153" frameborder="0" width="100%" height="650px"></iframe>
    </div>


    <!-- Templates -->
    <script type="x-tmpl-mustache" id="tmpl-penas">
        {{# opciones_penas }}
        <a data-role="button" class="ui-link ui-btn ui-btn-inline ui-shadow ui-corner-all  ui-btn-b" href="#" onclick="app.opcionPena('{{ opcion_pena }}')" alt="{{ opcion_pena }}">{{ opcion_pena }}</a>
        {{/ opciones_penas }}
    </script>

    <script type="x-tmpl-mustache" id="tmpl-li">
        <li class="ui-li ui-li-static ui-body-c" id="{{ idcrono }}">{{ mensaje }}</li>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-totales">
        <div class="ui-body ui-bar ui-bar-a text-center">
            Bar: {{ barrido }} | Tot: {{ total }} | Dif: {{ diff }} | Últ: {{ ultimo }}
            {{# antenas }}
            <br>
            1: {{ antena_1 }} | 2: {{ antena_2 }} | 3: {{ antena_3 }} | 4: {{ antena_4 }} |
            5: {{ antena_5 }} | 6: {{ antena_6 }} | 7: {{ antena_7 }} | 8: {{ antena_8 }}
            {{/ antenas }}
        </div>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-control">
        <span class="evento">{{ evento }}</span><br>
        <span class="codigo"><b>{{ codigo }}</b></span>
        - <span class="nombre">{{ nombre }}</span>
        <span class="tipo">({{ tipo }})</span>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-cargando">
        <a href="#" onclick="app.cancel($(this))" class="ui-icon-delete ui-btn-icon-left" id="{{ idlectura }}">
            <span class="idcrono" style="display: none;">{{ idcrono }}</span>
            <span class="idlectura" style="display: none;">{{ idlectura }}</span>
            <span class="tiempo" style="display: none;">{{ tiempo }}</span>
        </a>
        <span class="idparticipante">{{ idparticipante }}</span><br>
        <span class="contador">{{ idcrono }}</span>
        - <span class="hora">{{ hora }}</span>
        - <span class="accion">{{ accion }} {{ tagID }}</span>
        <a href="#" onclick="app.star($(this))" class="ui-icon-nostar ui-btn-icon-right">
            <span class="resaltar" style="display: none;">{{ resaltar }}</span>
        </a>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-lectura">
        {{^ extra }}
        <a href="#" onclick="app.mod($(this))" class="ui-icon-edit ui-btn-icon-left" id="{{ idlectura }}">
            <span class="idcrono" style="display: none;">{{ idcrono }}</span>
            <span class="idlectura" style="display: none;">{{ idlectura }}</span>
            <span class="tiempo" style="display: none;">{{ tiempo }}</span>
        </a>
        {{/ extra }}
        {{# extra }}
        <a href="#" class="ui-icon-check ui-btn-icon-left" id="{{ idlectura }}">
        </a>
        {{/ extra }}
        <span class="idparticipante">{{ idparticipante }}</span>
        {{# extra }}
        - <span class="extra">{{ extra }}</span>
        {{/ extra }}
        {{^ extra }}
        - <span class="tiempo_listo">{{ tiempo_listo }}</span>
        {{/ extra }}
        - <span class="nombre">{{ nombre }}</span><br>
        <span class="contador">{{ idcrono }}</span>
        - <span class="hora">{{ hora }}</span>
        - <span class="resultado">{{ resultado }}</span>
        <span class="accion">{{ accion }} {{ tagID }}</span>
        <a href="#" onclick="app.star($(this))" class="ui-icon-nostar ui-btn-icon-right">
            <span class="resaltar" style="display: none;">{{ resaltar }}</span>
        </a>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-baja">
        <a href="#" class="ui-icon-noicon ui-btn-icon-left" id="{{ idlectura }}">
            <span class="idcrono" style="display: none;">{{ idcrono }}</span>
            <span class="idlectura" style="display: none;">{{ idlectura }}</span>
            <span class="tiempo" style="display: none;">{{ tiempo }}</span>
        </a>
        <del>
        <span class="idparticipante">{{ idparticipante }}</span>
        - <span class="tiempo_listo">{{ tiempo_listo }}</span>
        - <span class="nombre">{{ nombre }}</span><br>
        <span class="contador">{{ idcrono }}</span>
        - <span class="hora">{{ hora }}</span>
        </del>
        - <span class="resultado">{{ resultado }}</span>
        <span class="accion">{{ accion }} {{ tagID }}</span>
        <a href="#" onclick="app.star($(this))" class="ui-icon-nostar ui-btn-icon-right">
            <span class="resaltar" style="display: none;">{{ resaltar }}</span>
        </a>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-error">
        <a href="#" onclick="app.mod($(this))" class="re-enviar ui-icon-edit ui-btn-icon-left" id="{{ idcrono }}">
            <span class="idcrono" style="display: none;">{{ idcrono }}</span>
        </a>
        <span class="tiempo" style="display: none;">{{ tiempo }}</span>
        <span class="idparticipante">{{ idparticipante }}</span><br>
        <span class="tagID" style="display: none;">{{ tagID }}</span>
        <span class="contador">{{ idcrono }}</span>
        - <span class="hora">{{ hora }}</span>
        - <span class="resultado">{{ resultado }}</span>
        <span class="accion">{{ accion }} {{ tagID }}</span>

        <a href="#" onclick="app.star($(this))" class="ui-icon-nostar ui-btn-icon-right">
            <span class="resaltar" style="display: none;">{{ resaltar }}</span>
        </a>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-default">
        <span class="json">ERROR: {{ json }}</span>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-esperando">
        <a href="#" onclick="app.esperando($(this))" class="esperando ui-icon-check ui-btn-icon-left">
            <span class="idcrono" style="display: none;">{{ idcrono }}</span>
        </a>
        <span class="idparticipante">{{ idparticipante }}</span>
        <span class="tagID" style="display: none;">{{ tagID }}</span>
        <span class="tiempo"> </span><br>
        <span class="contador">{{ idcrono }}</span>
        - <span class="resultado">{{ resultado }}</span>
        <span class="accion">{{ accion }} {{ tagID }}</span>
        <a href="#" onclick="app.star($(this))" class="ui-icon-nostar ui-btn-icon-right">
            <span class="resaltar" style="display: none;">{{ resaltar }}</span>
        </a>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-pena">
        <a href="#" onclick="" class="ui-icon-delee ui-btn-icon-left" id="{{ idpena }}">
            <span class="idcrono" style="display: none;">{{ idcrono }}</span>
            <span class="idcambiotiempo" style="display: none;">{{ idcambiotiempo }}</span>
        </a>
        <span class="idparticipante">{{ idparticipante }}</span>
        - <span class="nombre">{{ nombre }}</span><br>
        <span class="contador">{{ idcrono }}</span>
        - <span class="tiempo">{{ tiempo }} seg</span>
        - <span class="resultado">{{ resultado }}</span>
        <span class="accion">{{ accion }} {{ tagID }}</span>
        <a href="#" onclick="app.star($(this))" class="ui-icon-nostar ui-btn-icon-right">
            <span class="resaltar" style="display: none;">{{ resaltar }}</span>
        </a>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-tag">
        <a href="#" onclick="" class="ui-icon-check ui-btn-icon-left">
        </a>
        <span class="idparticipante">{{ idparticipante }}</span>
        - <span class="nombre">{{ nombre }}</span><br>
        <span class="contador">{{ idcrono }}</span>
        - <span class="resultado">{{ resultado }}</span>
        <span class="accion">{{ accion }} {{ tagID }}</span>
        <a href="#" onclick="app.star($(this))" class="ui-icon-nostar ui-btn-icon-right">
            <span class="resaltar" style="display: none;">{{ resaltar }}</span>
        </a>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-edit">
    <div id="editForm">
        <div style="padding:10px 20px; text-align: center;">
            <input type="hidden" name="idcrono" value="{{ idcrono }}" />
            <input type="hidden" name="idlectura" value="{{ idlectura }}" />
            <input type="hidden" name="resultado" value="{{ resultado }}" />
            <input type="hidden" name="hora" value="{{ hora }}" />
            <input type="hidden" name="tiempo" value="{{ tiempo }}" />
            <input type="hidden" name="tiempo_listo_original" value="{{ tiempo_listo }}" />

            <label>Participante:<br />
                <input name="idparticipante" type="number" pattern="[0-9]*" inputmode="numeric" value="{{ idparticipante }}" style="width:100px;" onClick="this.select();" />
            </label>

            <label>Hora:<br />
                <input name="hora_1" onkeyup="app.keyupHora()" type="number" pattern="[0-9]{2}" onchange="app.changeHora()" value="{{ hora_1 }}" inputmode="numeric" style="width:50px;"  onClick="this.select();" />
                <b>:</b>
                <input name="hora_2" onkeyup="app.keyupHora()" type="number" pattern="[0-9]{2}" onchange="app.changeHora()" value="{{ hora_2 }}" inputmode="numeric" style="width:50px;"  onClick="this.select();" />
                <b>:</b>
                <input name="hora_3" onkeyup="app.keyupHora()" type="number" pattern="[0-9]{2}" onchange="app.changeHora()" value="{{ hora_3 }}" inputmode="numeric" style="width:50px;"  onClick="this.select();" />
                <b>.</b>
                <input name="hora_4" onkeyup="app.keyupHora()" type="number" pattern="[0-9]{3}" onchange="app.changeHora()" value="{{ hora_4 }}" inputmode="numeric" style="width:70px;"  onClick="this.select();" />
            </label>

            {{ #idlectura }}
            <label>Tiempo:<br />
                <input type="text" name="tiempo_listo" onchange="app.changeTiempo()" value="{{ tiempo_listo }}" disabled />
            </label>
            {{ /idlectura }}
            {{ ^idlectura }}
                <input type="hidden" name="tiempo_listo" value="{{ tiempo_listo }}" />
            {{ /idlectura }}

            <a onclick="app.menos();" href="#" class="ui-btn ui-btn-icon-left ui-icon-arrow-d ui-btn-icon-notext ui-btn-inline"></a>
            <a onclick="app.mas()" href="#" class="ui-btn ui-btn-icon-left ui-icon-arrow-u ui-btn-icon-notext ui-btn-inline"></a><br />

            <button type="submit" onclick="app.ok()" class="ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline">Modificar</button>
            <button type="submit" onclick="app.baja()" class="ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline">Borrar</button>
        </div>
    </div>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-configurar">
    <div id="configurarForm">
        <div style="padding:10px 20px; text-align: center;">
            <label>Código del Control:<br />
                <input type="text" name="codigo" value="{{ codigo }}" maxlength="4" />
            </label>
            <label>Cronometrador:<br />
                <input type="text" name="cronometrador" value="{{ cronometrador }}" maxlength="40" />
            </label>

            {{ #compatible_nube_hibrida }}
            <label>Nube híbrida:<br />
                <input type="checkbox" name="nube_hibrida" {{ nube_hibrida }}/>
            </label>
            {{ /compatible_nube_hibrida }}

            <button type="submit" onclick="app.configurar()" class="ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline">Configurar</button>

            <hr>

            {{ #compatible_hardware }}
            <label>Hardware configurado:<br />
                {{ conectar_hardware }}
            </label>

            <button type="submit" onclick="app.configHardware()" class="ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline">Hardware</button>

            {{ /compatible_hardware }}

            <p style="font-size: small">v {{ version }}</p>
        </div>
    </div>
    </script>

    <script type="x-tmpl-mustache" id="tmpl-hardware">
    <div id="hardwareForm">
        <div style="padding:10px 20px; text-align: center;">

            <h4>Dispositivo 1</h4>
            <select name="dispositivo1">
            {{ #android }}
                <option value="">No conectado</option>
                <option value="cronopic4">Fotocélula Bluetooth</option>
                <option value="cronopic6">Fotocélula Bluetooth Low Energy</option>
            {{ /android }}
            {{ #windows }}
                <option value="">No conectado</option>
                <option value="reader1">Reader UHF RFID por USB</option>
                <option value="reader2">Reader UHF RFID por LAN</option>
                <option value="reader3">USB Desktop UHF RFID</option>
                <option value="chafon_810">Reader Chafon 810 por LAN</option>
                <option value="cronopic4">Fotocélula Cronopic Bluetooth</option>
                <option value="cronopic3">Fotocélula Cronopic Wifi Multicanal</option>
                <option value="cronopic1">Fotocélula Cronopic Wifi CH1</option>
                <option value="cronopic2">Fotocélula Cronopic Wifi CH2</option>
                <option value="cronopic5">Fotocélula Cronopic con Reloj Interno</option>
                <option value="cronopic6">Fotocélula Bluetooth Low Energy</option>
                <option value="fotocelula1">Fotocélula Genérica por Espacio</option>
            {{ /windows }}
            </select>
            <br />
            {{ #windows }}
            <select name="puerto1">
                <option value="">No utilizar COM</option>
                <option value="COM1">COM1</option>
                <option value="COM2">COM2</option>
                <option value="COM3">COM3</option>
                <option value="COM4">COM4</option>
                <option value="COM5">COM5</option>
                <option value="COM6">COM6</option>
                <option value="COM7">COM7</option>
                <option value="COM8">COM8</option>
                <option value="COM9">COM9</option>
                <option value="COM10">COM10</option>
                <option value="COM11">COM11</option>
                <option value="COM12">COM12</option>
                <option value="COM13">COM13</option>
                <option value="COM14">COM14</option>
                <option value="COM15">COM15</option>
                <option value="COM16">COM16</option>
                <option value="COM17">COM17</option>
                <option value="COM18">COM18</option>
                <option value="COM19">COM19</option>
                <option value="COM20">COM20</option>
            </select>
            <br />
            {{ /windows }}
            {{ #linux  }}
            <select name="puerto1">
                <option value="/dev/ttyUSB0">/dev/ttyUSB0</option>
                <option value="/dev/ttyUSB1">/dev/ttyUSB1</option>
                <option value="/dev/ttyUSB2">/dev/ttyUSB2</option>
                <option value="/dev/ttyUSB3">/dev/ttyUSB3</option>
            </select>
            <br />
            {{ /linux  }}
            </select>
            <br />
            {{ #windows }}
            <h4>Configuración de reader</h4>

            <label>Modo:
            <select name="rfid_crono" onchange="isTagCodigo()">
                <option value="">Desactivado</option>
                <option value="test">Pruebas</option>
                <option value="real">Cronometraje Real Time</option>
                <option value="fast">Cronometraje Fast Switch</option>
                <option value="tag">Asignar chip</option>
                <option value="tag_codigo">Grabar chip</option>
            </select>
            </label>

            <div id="tag_codigo" style="display: none">
                <label>Prefijo <input type="text" name="tag_prefijo" maxlength="8" style="width: 100px;" /></label>
            </div>

            <label>Ignorar repetidos:
            <select name="rebote">
                <option value="0">Nunca</option>
                <option value="5000">5 seg</option>
                <option value="30000">30 seg</option>
                <option value="300000">5 min</option>
                <option value="1800000">30 min</option>
                <option value="86400000">24 hs</option>
            </select>
            </label>

            <!--
            <label>Intervalo de lectura:
            <select name="intervalo">
                <option value="25">25 ms</option>
                <option value="50">50 ms</option>
                <option value="100">100 ms</option>
                <option value="250">250 ms</option>
                <option value="500">500 ms</option>
                <option value="1000">1 seg</option>
            </select>
            </label>
            -->

            <label>Reproducir sonido: <input type="checkbox" name="sonido"/></label>

            <h4>Antenas conectadas:</h4>
            <label style="display: inline;">1 <input type="checkbox" name="antena_1"/></label>
            <label style="display: inline;">2 <input type="checkbox" name="antena_2"/></label>
            <label style="display: inline;">3 <input type="checkbox" name="antena_3"/></label>
            <label style="display: inline;">4 <input type="checkbox" name="antena_4"/></label>
            <br />
            <label style="display: inline;">5 <input type="checkbox" name="antena_5"/></label>
            <label style="display: inline;">6 <input type="checkbox" name="antena_6"/></label>
            <label style="display: inline;">7 <input type="checkbox" name="antena_7"/></label>
            <label style="display: inline;">8 <input type="checkbox" name="antena_8"/></label>

            <br />
            {{ /windows }}
            {{ #linux  }}
            <select name="puerto2">
                <option value="/dev/ttyUSB0">/dev/ttyUSB0</option>
                <option value="/dev/ttyUSB1">/dev/ttyUSB1</option>
                <option value="/dev/ttyUSB2">/dev/ttyUSB2</option>
                <option value="/dev/ttyUSB3">/dev/ttyUSB3</option>
            </select>
            <br />
            {{ /linux  }}

            <br />

            <button type="submit" onclick="app.hardware()" class="ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline">Configurar</button>
        </div>
    </div>
    </script>
    <!-- End Templates -->

</body>
</html>
