
var chafon_810 = {

    'nombre': 'Reader Chafon 810 por LAN',
    'config': {
        'enter': true,
        'host': '*************',
        'port': '2022',
        'interval': 25
    },

    'connection': null,
    'interval': null,
    'antena': 0,
    'tagsRecientes': [],
    'ultimoTag': '',
    'tiempoReciente': 10000, // En milisegundos

    totales: {
        barrido: 1,
        total: 0,
        diff: 0,
        ultimo: 0,
        antenas: [0,0,0,0,0,0,0,0,0],
    },

    conectar: function () {
        var that = this;
        this.tagsRecientes = [];
        this.antena = 0;
        this.cambio = true;
        this.continuar = false;
        this.totales = {
            barrido: 0,
            total: 0,
            diff: 0,
            ultimo: 0,
            antenas: [0,0,0,0,0,0,0,0,0],
        };
        var net = require('net');
        this.connection = new net.Socket();

        this.connection.on('error', function(error) {
            hardware.desconectar();
            if (typeof error.message != 'undefined'
                && error.message.indexOf('Unknown error code 31')) // Puerto colgado
                messageToUser = 'El puerto dirección IP del Reader no responde.\nRevise la correcta configuración de su red, intente re-conectar el dispositivo y reiniciar la app.\nSi no funciona contacte nuestro soporte técnico.\n';
            else
                messageToUser = 'No hay conexión con el reader\nReporte este mensaje técnico interno al soporte: ' + error;
            alert(messageToUser);
        });

        this.connection.connect(this.config.port, this.config.host, function() {

            alerta(that.nombre + ' conectado en el host ' + that.config.host + ':' + that.config.port);
            // Podemos hacer un control de temperatura y cuelgue cada 5 segundos
            // that.interval = setInterval(function () {that.consultar()}, 1000);

            // $("#consola").show();
            that.consultar('firmware');
        });

        this.connection.on('data', function(data) {
            that.recibir(data);
        });

        $("#totales").show();
    },

    desconectar: function () {
        var that = this;

        window.rfid_crono = "";
        this.consultar('stop');
        setTimeout(() => { that.connection.destroy() }, 1000);
    },

    consultar: function (command, params = false) {

        let hex;
        if (['firmware', 'params', 'stop'].includes(command)) {
            hex = chafon.hexEncode({ cmd: command });

        } else if (['test', 'real', 'fast', 'tag', 'tag_codigo'].includes(command)) {
            hex = chafon.hexEncode({ cmd: 'real' });

        } else if (command == 'antena') {
            let antenas = (window.antena_8 ? "1" : "0")
                + (window.antena_7 ? "1" : "0")
                + (window.antena_6 ? "1" : "0")
                + (window.antena_5 ? "1" : "0")
                + (window.antena_4 ? "1" : "0")
                + (window.antena_3 ? "1" : "0")
                + (window.antena_2 ? "1" : "0")
                + (window.antena_1 ? "1" : "0");
                antenas = parseInt(antenas, 2).toString(16).toUpperCase();
                if (antenas.length < 2)
                antenas = '0' + antenas;
            params = params.substr(2, 12) + antenas + params.substr(16, 36);
            hex = chafon.hexEncode({ cmd: 'antena', params: params });

        } else {
            alertaConsola('Command ' + command + ' not found');
        }

        var buffer = Buffer.from(hex, 'hex');
        this.connection.write(buffer, function(err) {
            alertaConsola('S' + hex);
            if (err)
                alertaConsola('S' + hex + ' error ', err.message);
        });

    },

    recibir: function (data) {

        var that = this;
        dataHex = data.toString('hex').toUpperCase();
        alertaConsola('R' + dataHex );

        chafon.splitHex(dataHex).forEach( function(hex, i, array) {

            let dataJSON = chafon.hexDecode(hex);
            console.log(JSON.stringify(dataJSON));

            if (dataJSON.error) {
                alerta('Error: ' + dataJSON.error + ' | Code: ' + dataJSON.errorCode);
            }

            if (dataJSON.cmd == 'firmware') {
                alerta('Firmware: ' + dataJSON.firmware);
                that.consultar('params');

            } else if (dataJSON.cmd == 'params') {
                that.consultar('antena', dataJSON.hex_params);

            } else if (dataJSON.cmd == 'antena' && window.rfid_crono) {
                that.consultar('real');

            // Me parece que no tiene aviso de antena-missing
            // } else if (dataJSON.error == 'antena-missing') {
            //     let antenaMissing = isNaN(dataJSON.antena) ? that.antena : dataJSON.antena;
            //     // window['antena_' + antenaMissing] = false;
            //     that.totales.antenas[antenaMissing] = '⚠️';
            //     if (window.rfid_crono != 'fast')
            //         that.consultar('antena');

            } else if (dataJSON.cmd == 'real' && window.rfid_crono) {
                that.totales.barrido++;
                that.consultar('real');

            } else if (dataJSON.cmd == 'epc') {
                that.epc(dataJSON.epc, dataJSON.antena);

            } else if (dataJSON.cmd == 'stop' && !dataJSON.error) {
                alerta(that.nombre + ' desconectado');
            }

            that.stats();

        });

        return;
    },

    epc: function (tagID, antena = false) {
        if (window.sonido)
            bep.play();
        alertaConsola('EPC ' + this.antena + ' ' + tagID);
        this.totales.ultimo++;
        this.totales.total++;
        this.totales.antenas[antena] = !isNaN(this.totales.antenas[antena])
            ? this.totales.antenas[antena] + 1
            : 1;
            console.log('le sumo a ' + antena + ' que tiene ', this.totales.antenas[antena])
        if (!this.tagReciente(tagID)) {
            $("#tagID").html(tagID);
            let idparticipante = $("#idparticipante").html();
            if (!isNaN(idparticipante)
                && (window.rfid_crono == 'tag' || window.rfid_crono == 'tag_codigo'))
                app.tag(window.rfid_crono);

            else if (window.rfid_crono == 'real' || window.rfid_crono == 'fast' || window.rfid_crono == 'tag')
                app.crono();
        }

    },

    stats: function () {

        console.log(this.totales)
        if (window.rfid_crono == 'test' || window.rfid_crono == 'real' || window.rfid_crono == 'fast')
            $("#totales").html(renderTemplate('totales', {
                barrido: this.totales.barrido,
                total: this.totales.total,
                diff: this.tagsRecientes.length,
                ultimo: this.totales.ultimo,
                // antenas: window.rfid_crono != 'fast',
                antenas: true,
                antena_1: window['antena_1'] ? this.totales.antenas[1] : '-',
                antena_2: window['antena_2'] ? this.totales.antenas[2] : '-',
                antena_3: window['antena_3'] ? this.totales.antenas[3] : '-',
                antena_4: window['antena_4'] ? this.totales.antenas[4] : '-',
                antena_5: window['antena_5'] ? this.totales.antenas[5] : '-',
                antena_6: window['antena_6'] ? this.totales.antenas[6] : '-',
                antena_7: window['antena_7'] ? this.totales.antenas[7] : '-',
                antena_8: window['antena_8'] ? this.totales.antenas[8] : '-',
            }));

        return;
    },

    tagReciente: function (tagID) {

        var time = new Date().getTime();

        for (i = 0; i < this.tagsRecientes.length; i++) {
            if (this.tagsRecientes[i].tagID == tagID) {
                if (this.tagsRecientes[i].time > (time - window.rebote)) {
                    return true;
                } else {
                    this.tagsRecientes[i].time = time;
                    return false;
                }
            }
        }

        let reciente = {'time': time, 'tagID': tagID};
        this.tagsRecientes.push(reciente);
        return false;

    }

}
