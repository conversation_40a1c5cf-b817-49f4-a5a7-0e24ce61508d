
var reader1 = {

    'nombre': 'Reader UHF RFID por USB',
    'config': {
        'enter': true,
        'baudios': 115200,
        'puerto': 0,
        'interval': 25
    },

    'connection': null,
    'interval': null,
    'antena': 0,
    'tagsRecientes': [],
    'ultimoTag': '',
    'tiempoReciente': 10000, // En milisegundos

    totales: {
        barrido: 1,
        total: 0,
        diff: 0,
        ultimo: 0,
        antenas: [0,0,0,0,0,0,0,0,0],
    },

    conectar: function () {
        var that = this;
        this.tagsRecientes = [];
        this.antena = 0;
        this.cambio = true;
        this.continuar = false;
        this.totales = {
            barrido: 0,
            total: 0,
            diff: 0,
            ultimo: 0,
            antenas: [0,0,0,0,0,0,0,0,0],
        };

        const { SerialPort } = require('serialport');
        this.connection = new SerialPort({path: this.config.puerto, baudRate: this.config.baudios, encoding: 'hex'});

        this.connection.on('error', function(error) {
            hardware.desconectar();
            if (typeof error.message != 'undefined'
                || error.message.indexOf('Unknown error code 31')) // Puerto colgado
                messageToUser = 'El puerto ' + that.config.puerto + ' no responde.\nRevise la correcta configuración, intente re-conectar el dispositivo y reiniciar la app.\nSi no funciona contacte nuestro soporte técnico.\n';
            else
                messageToUser = 'No hay conexión con el reader\nReporte este mensaje técnico interno al soporte: ' + error;
            alert(messageToUser);
        });

        this.connection.on('open', function () {

            alerta(that.nombre + ' conectado en el puerto ' + that.config.puerto);
            // Podemos hacer un control de temperatura y cuelgue cada 5 segundos
            // that.interval = setInterval(function () {that.consultar()}, 1000);

            // $("#consola").show();
            that.consultar('firmware');
        });

        this.connection.on('data', function(data) {
            that.recibir(data);
        });

        $("#totales").show();
    },

    desconectar: function () {
        var that = this;
        // clearInterval(this.interval);
        if (typeof this.connection != 'undefined') {
            this.connection.close(function (err) {
                alerta(that.nombre + ' desconectado del puerto ' + that.config.puerto);
                console.log(that.nombre + ' desconectado del puerto ' + that.config.puerto, err);
            });
        }
    },

    consultar: function (command) {

        let hex;
        if (command == 'firmware' || command == 'temperature' || command == 'fast') {
            hex = invelion.hexEncode({ cmd: command });

        } else if (['test', 'real', 'tag', 'tag_codigo'].includes(command)) {
            hex = invelion.hexEncode({ cmd: 'real' });

        } else if (command == 'antena') {
            hex = invelion.hexEncode({ cmd: 'antena', antena: this.nextAntena() });

        } else {
            alertaConsola('Command ' + command + ' not found');
        }

        var buffer = Buffer.from(hex, 'hex');

        this.connection.write(buffer, function(err) {
            alertaConsola('S' + hex);
            if (err)
                alertaConsola('S' + hex + ' error ', err.message);
        });

    },

    recibir: function (data) {

        var that = this;
        dataHex = data.toString('hex').toUpperCase();
        alertaConsola('R' + dataHex );

        invelion.splitHex(dataHex).forEach( function(hex, i, array) {

            let dataJSON = invelion.hexDecode(hex);
            // console.log(JSON.stringify(dataJSON));

            if (dataJSON.error && dataJSON.error != 'antena-missing') {
                alerta('Error: ' + dataJSON.error + ' | Code: ' + dataJSON.errorCode);
            }

            if (dataJSON.cmd == 'firmware') {
                alerta('Firmware: ' + dataJSON.firmware);

                if (dataJSON.firmware != '08.02' && dataJSON.firmware != '06.09') {
                    alerta('Firmware ' + dataJSON.firmware + ' no conocido');
                }
                that.consultar('temperature');

            } else if (dataJSON.cmd == 'temperature') {
                alerta('Temperatura: ' + dataJSON.temperature + '°C');

                if (dataJSON.temperature > 50) {
                    alerta('Temperatura ' + dataJSON.temperature + '°C muy alta');
                } else if (dataJSON.temperature < 25) {
                    alerta('Temperatura ' + dataJSON.temperature + '°C muy baja');
                }

                if (window.rfid_crono == 'fast') {
                    that.consultar(window.rfid_crono);
                } else {
                    this.antena = 0;
                    that.consultar('antena');
                }

            } else if (dataJSON.error == 'antena-missing') {
                let antenaMissing = isNaN(dataJSON.antena) ? that.antena : dataJSON.antena;
                // window['antena_' + antenaMissing] = false;
                that.totales.antenas[antenaMissing] = '⚠️';
                if (window.rfid_crono != 'fast')
                    that.consultar('antena');

            } else if (dataJSON.cmd == 'antena') { // && es la misma antena (sino pasó algo raro)
                that.consultar('real');

            } else if (dataJSON.cmd == 'epc') {
                that.epc(dataJSON.epc, dataJSON.antena);

            } else if (dataJSON.cmd == 'real') {
                that.consultar('antena');

            } else if (dataJSON.cmd == 'fast') {
                that.totales.barrido++;
                that.consultar('fast');
            }

            that.stats();

        });

        return;
    },

    epc: function (tagID, antena = false) {
        if (window.sonido)
            bep.play();
        alertaConsola('EPC ' + this.antena + ' ' + tagID);
        this.totales.ultimo++;
        this.totales.total++;
        if (!antena || window.rfid_crono != 'fast')
            antena = this.antena;
        this.totales.antenas[antena] = !isNaN(this.totales.antenas[antena])
            ? this.totales.antenas[antena] + 1
            : 1;
        if (!this.tagReciente(tagID)) {
            $("#tagID").html(tagID);
            let idparticipante = $("#idparticipante").html();
            if (!isNaN(idparticipante)
                && (window.rfid_crono == 'tag' || window.rfid_crono == 'tag_codigo'))
                app.tag(window.rfid_crono);

            else if (window.rfid_crono == 'real' || window.rfid_crono == 'fast' || window.rfid_crono == 'tag')
                app.crono();
        }

    },

    stats: function () {

        if (window.rfid_crono == 'test' || window.rfid_crono == 'real' || window.rfid_crono == 'fast')
            $("#totales").html(renderTemplate('totales', {
                barrido: this.totales.barrido,
                total: this.totales.total,
                diff: this.tagsRecientes.length,
                ultimo: this.totales.ultimo,
                // antenas: window.rfid_crono != 'fast',
                antenas: true,
                antena_1: window['antena_1'] ? this.totales.antenas[1] : '-',
                antena_2: window['antena_2'] ? this.totales.antenas[2] : '-',
                antena_3: window['antena_3'] ? this.totales.antenas[3] : '-',
                antena_4: window['antena_4'] ? this.totales.antenas[4] : '-',
                antena_5: window['antena_5'] ? this.totales.antenas[5] : '-',
                antena_6: window['antena_6'] ? this.totales.antenas[6] : '-',
                antena_7: window['antena_7'] ? this.totales.antenas[7] : '-',
                antena_8: window['antena_8'] ? this.totales.antenas[8] : '-',
            }));

        return;
    },

    tagReciente: function (tagID) {

        var time = new Date().getTime();

        for (i = 0; i < this.tagsRecientes.length; i++) {
            if (this.tagsRecientes[i].tagID == tagID) {
                if (this.tagsRecientes[i].time > (time - window.rebote)) {
                    return true;
                } else {
                    this.tagsRecientes[i].time = time;
                    return false;
                }
            }
        }

        let reciente = {'time': time, 'tagID': tagID};
        this.tagsRecientes.push(reciente);
        return false;

    },

    nextAntena: function () {

        let antena;
        do {
            if (this.antena >= 8) {
                this.antena = 1;
                this.totales.barrido++;
                this.totales.ultimo = 0;
            } else {
                this.antena++;
            }
            antena = this.antena;
        } while (!window['antena_' + antena]);

        return antena;
    }

}
