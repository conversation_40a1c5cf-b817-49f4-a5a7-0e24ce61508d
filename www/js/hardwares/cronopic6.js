
var cronopic6 = {

    'config': {
        'enter': true,
        'puerto': false
    },

    'serialport': null,

    fotocelulas: [],
    tiempomuerto: 500,
    ultimo_corte: 0,

    conectar: function () {
        alert('Cronometraje Instántaneo necesita el permiso de ubicación mientras la app está en uso, sólo para conectarse con las Fotocélulas Bluetooth Low Energy. Sin este permiso puede usar la app pero no se conectará la fotocélula. El dato de la ubicación no es guardado en ningún momento.');

        var that = this;

        cronopic6.fotocelulas = [];

        ble.isLocationEnabled(() => {}, () => alerta("Debe encender la ubicación"));

        ble.isEnabled(() => {
            alerta("Buscando Fotocélula BLE");
            ble.scan([], 15,
                that.onDiscoverDevice,
                () => alerta("No se encuentran Fotocélulas BLE"));
        }, () => alerta("Bluetooth NO está encendido"));
        return true;
    },

    desconectar: function () {
        ble.disconnect(cronopic6.fotocelulas[0],
            () => alerta("Fotocélula BLE desconectada"));
        cronopic6.fotocelulas = [];
    },

    onDiscoverDevice: function (device) {

        // Sólo una conexión por el momento
        if (typeof cronopic6.fotocelulas != typeof undefined && cronopic6.fotocelulas.length)
            return false;

        if (device.name.toLowerCase().substring(0,8) == 'cronopic') {
            alerta('Encontrada ' + device.name);
            cronopic6.fotocelulas.push(device.id);
            ble.autoConnect(device.id, cronopic6.connectCallback, cronopic6.disconnectCallback);
        }

    },

    validar: function (data) {
        if (data[0] == 114) // r minúsculas; TODO: implementar con el 82 R mayúscula que es el control
            return true;
        else
            return false;
    },

    connectCallback: function (device) {
        alerta('Fotocélula BLE conectada');
        ble.startNotification(cronopic6.fotocelulas[0], "FFE0", "FFE1",
            cronopic6.recibir,
            () => alerta('Error conectando Fotocélula BLE'));
    },

    disconnectCallback: function () {
        ble.stopNotification(cronopic6.fotocelulas[0], "FFE0", "FFE1",
            () => alerta('Fotocélula BLE buscando reconectar'),
            () => alerta('Fotocélula BLE perdió conexión'));
    },

    validarTiempoMuerto: function () {
        var now = Date.now();

        if ((now - this.ultimo_corte) < this.tiempomuerto) {
            return false;
        }

        this.ultimo_corte = now;
        return true;
    },

    recibir: function (data) {
        var bytes = new Uint8Array(data);

        if (!cronopic6.validar(bytes))
            return false;

        if (!cronopic6.validarTiempoMuerto())
            return false;

        var lectura = $(".esperando").last();

        if (lectura.length)
            app.esperando(lectura);
        else
            app.crono();
    }

}
