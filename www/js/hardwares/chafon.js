
var chafon = {

    hexEncode: function (data) {

        let cmd = data.cmd;
        let hex = 'CF'; // Head
        let address = '01'; // Address
        hex += address;

        switch (cmd) {
            case 'firmware':
                hex += '0070'; // RFM_GET_DEVICEINFO
                hex += '00'; // Length
                break;

            case 'real':
                hex += '0001'; // RFM_INVENTORYISO_CONTINUE (Label inventory)
                hex += '05'; // Length
                hex += '00' // InvType: Inventory method: 0x00 represents the counting time
                    + '00000000'; // InvParam represents the counting time, the unit is: seconds, if the value is 0 it'll continued until the stop counting command is received
                break;

            case 'antena':
                hex += '0071'; // RFM_SET_ALL_PARAM
                hex += '19'; // Length
                hex += data.params; // Params with modified antenas
                break;

            case 'params': // RFM_GET_ALL_PARAM (Read all configurable parameters)
                hex += '0072'; // RFM_GET_ALL_PARAM
                hex += '00'; // Length
                break;

            case 'stop':
                hex += '0002'; // RFM_INVENTORY_STOP (stop counting)
                hex += '00'; // Length
                break;

            default:
                alertaConsola('HEX error: cmd ' + cmd + ' not found');
                break;
        }

        hex += chafon.checkSum(hex); // Add 2 bytes checksum
        return hex;

    },


    hexDecode: function (hex) {

        // Control de length
        let len = hex.substr(8, 2);
        if (parseInt(len, 16) != (hex.length - 14) / 2)
            console.log( { error: 'length' }, parseInt(len, 16), (hex.length - 14) / 2, hex );

        // Control de check
        let check = hex.substr(hex.length - 4, 4);
        if (check != chafon.checkSum(hex.substr(0, hex.length - 4)))
            console.log( { error: 'check' }, check, chafon.checkSum(hex.substr(0, hex.length - 4)), hex );

        // Control Address
        if (hex.substr(2, 2) != '01')
            console.log( { error: 'address' }, hex.substr(2, 2), '01' );

        let cmd = hex.substr(4, 4);
        let data = hex.substr(10, hex.length - 4);
        switch (cmd) {

            default:
                return { error: 'error-hex', cmd: '' };

            case '0070': // RFM_GET_DEVICEINFO
                let reader_info = {
                    status: data.substr(0, 2),
                    HardVer: hexToAscii(data.substr(2, 30)), // Ignoro desde el 30 al 64 porque son 0
                    FirmVer: hexToAscii(data.substr(66, 64)),
                    SN_code: data.substr(130, 24),
                    Reserve_I: data.substr(154, 64),
                    Reserve_II: data.substr(218, 64),
                    Reserve_III: data.substr(282, 24)
                };
                console.log('reader_info', reader_info);
                window.firmware = reader_info.HardVer + ' - ' + reader_info.FirmVer;
                return { error: false, cmd: 'firmware', firmware: window.firmware };

            case '0071': // // RFM_SET_ALL_PARAM
                if (data.substr(0, 2) != '00') // Status con ERROR
                    return {
                        error: 'antena' + data.substr(0, 2),
                        cmd: 'antena'
                    };
                else
                    return { error: false, cmd: 'antena' };

            case '0072': // RFM_GET_ALL_PARAM (Read all configurable parameters)
                let reader_params = {
                    status: data.substr(0, 2),
                    addr: data.substr(2, 2),
                    RFIDPRO: data.substr(4, 2),
                    WorkMode: data.substr(6, 2),
                    Interface: data.substr(8, 2),
                    Baudrate: data.substr(10, 2),
                    WGSet: data.substr(12, 2),
                    Ant: data.substr(14, 2),
                    RfidFreq: data.substr(16, 16),
                    RfidPower: data.substr(32, 2),
                    InquiryArea: data.substr(34, 2),
                    QValue: data.substr(36, 2),
                    Session: data.substr(38, 2),
                    AcsAddr: data.substr(40, 2),
                    AcsDataLen: data.substr(42, 2),
                    FilterTime: data.substr(44, 2),
                    TriggerTime: data.substr(46, 2),
                    BuzzerTime: data.substr(48, 2),
                    Interval: data.substr(50, 2)
                };
                console.log('reader_params', reader_params);
                return { error: false, cmd: 'params', params: reader_params, hex_params: data.substr(0, 52) };

            case '0001': // RFM_INVENTORYISO_CONTINUE (Label inventory)
                if (len == '01' && data.substr(0, 2) == '12')
                    return {
                        error: false,
                        cmd: 'real'
                    };

                else if (data.substr(0, 2) != '00') // Status con ERROR
                    return {
                        error: 'error-' + data.substr(0, 2),
                        cmd: 'real'
                    };

                else {
                    let retorno = {
                        error: false,
                        cmd: 'epc',
                        rrss: data.substr(2, 4),
                        antena: parseInt(data.substr(6, 2)),
                        channel: data.substr(8, 2),
                        pc: data.substr(10, 2)
                    };
                    switch (retorno.pc) {
/*                         case '14':
                            retorno.epc = data.substr(6, 8);
                            break;
 */
                        case '06':
                            retorno.epc = data.substr(12, 12);
                            break;
                        case '0C':
                            retorno.epc = data.substr(12, 24);
                            break;
                        default:
                            retorno.error = 'pc-' + retorno.pc;
                            console.log('error-pc', retorno.pc, data)
                            break;
                    }
                    return retorno;
                }

            case '0002': // RFM_INVENTORY_STOP (stop counting)
                if (data.substr(0, 2) == '00') // Status con STOP OK
                    return { error: false, cmd: 'stop' };
                else
                    return {
                        error: 'error-stop',
                        cmd: 'stop'
                    };
        }

    },

    splitHex: function (dataHex) {

        let hexs = [];
        let j = 0;
        let len = 0;

        do {
            if (dataHex.substr(j, 2) != 'CF') {
                return hexs;
            }

            len = parseInt(dataHex.substr(j + 8, 2), 16) * 2;
            hexs.push(dataHex.substr(j, len + 14));
            j += len + 14;

        } while (dataHex.length >= j)

        return hexs;

    },

    checkSum: function (buff) {
        const PRESET_VALUE = 0xFFFF;
        const POLYNOMIAL = 0x8408;
        const byteArray = new Uint8Array(buff.length / 2);

        for (let i = 0; i < byteArray.length; i++) {
            byteArray[i] = parseInt(buff.substr(2 * i, 2), 16);
        }

        let buffLen = byteArray.length;
        let uSum = PRESET_VALUE;
        let ucI, ucJ;

        for (ucI = 0; ucI < buffLen; ucI++) {
            uSum = uSum ^ byteArray[ucI];
            for (ucJ = 0; ucJ < 8; ucJ++) {
                if (uSum & 0x0001) {
                    uSum = (uSum >>> 1) ^ POLYNOMIAL;
                } else {
                    uSum = uSum >>> 1;
                }
            }
        }

        uSum &= PRESET_VALUE; // Set Hex
        // Asegurar que el valor CRC es un entero de 16 bits
        uSum = uSum.toString(16).toUpperCase();
        while (uSum.length < 4)
            uSum = '0' + uSum;
        return uSum;
    }

}