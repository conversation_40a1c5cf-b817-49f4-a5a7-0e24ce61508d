
var invelion = {

    hexEncode: function (data) {

        let cmd = data.cmd;
        let hex = 'A0'; // Head
        let address = '01'; // Address

        switch (cmd) {
            case 'firmware':
                hex += '03'; // Length
                hex += address;
                hex += '72'; // cmd_get_firmware_version
                break;

            case 'temperature':
                hex += '03'; // Length
                hex += address;
                hex += '7B'; // cmd_get_reader_temperature
                break;

            case 'real':
                hex += '04'; // Length
                hex += address;
                hex += '89'; // cmd_real_time_inventory
                hex += '01' // Data: Session and Inventoried Flags S1
                break;

            case 'antena':
                hex += '04'; // Length
                hex += address;
                hex += '74'; // cmd_set_work_antenna
                hex += '0' + (data.antena - 1); // Data: Antena
                break;

            case 'fast':
                hex += window.firmware == '08.02' ? '20' : '0D'; // Length: Version 8.2 permite 8 antenas, mientras que 6.9 sólo 4
                hex += address;
                hex += '8A'; // cmd_fast_switch_ant_inventory
                hex += "00" + (window.antena_1 ? "01" : "00")
                hex += "01" + (window.antena_2 ? "01" : "00")
                hex += "02" + (window.antena_3 ? "01" : "00")
                hex += "03" + (window.antena_4 ? "01" : "00")
                if (window.firmware == '08.02') { //  Length: Version 8.2 permite 8 antenas, mientras que 6.9 sólo 4
                    hex += "04" + (window.antena_5 ? "01" : "00")
                    hex += "05" + (window.antena_6 ? "01" : "00")
                    hex += "06" + (window.antena_7 ? "01" : "00")
                    hex += "07" + (window.antena_8 ? "01" : "00")
                    hex += "0000000000000000000000" // Reserved + Session + Target
                }
                hex += "00" // Intervalo en 0
                hex += "0A" // Repeat 10
                break;

            default:
                alertaConsola('HEX error: cmd ' + cmd + ' not found');
                break;
        }

        hex += invelion.checkSum(hex); // Add 2 bytes checksum

        return hex;

    },

    hexDecode: function (hex) {

        // Control de length
        let len = hex.substr(2, 2);
        if (parseInt(len, 16) != (hex.length - 4) / 2)
            console.log( { error: 'length' }, parseInt(len, 16), (hex.length - 4) / 2 );

        // Control de check
        let check = hex.substr(hex.length - 2, 2);
        if (check != invelion.checkSum(hex.substr(0, hex.length - 2)))
            console.log( { error: 'check' }, check, invelion.checkSum(hex.substr(0, hex.length - 2)) );

        // Control Address
        if (hex.substr(4, 2) != '01')
            console.log( { error: 'address' }, hex.substr(4, 2), '01' );

        let cmd = hex.substr(6, 2);
        let data = hex.substr(8, hex.length - 10);
        switch (cmd) {

            default:
                return { error: 'error-hex', cmd: '' };

            case '72': // cmd_get_firmware_version
                window.firmware = data.substr(0, 2) + '.' + data.substr(2, 2);
                return { error: false, cmd: 'firmware', firmware: window.firmware };

            case '7B': // cmd_get_reader_temperature
                window.temperature = parseInt(data.substr(2, 2), 16);
                if (data.substr(0, 2) == '00')
                    window.temperature *= -1;
                return { error: false, cmd: 'temperature', temperature: window.temperature };

            case '74': // cmd_set_work_antenna
                if (data.substr(0, 2) == '10')
                    return { error: false, cmd: 'antena' };
                else
                    return { error: 'antena-' + data.substr(0, 2), cmd: 'antena' };

            case '89': // cmd_real_time_inventory
                if (len == '04' && data.substr(0, 2) == '22')
                    return {
                        error: 'antena-missing',
                        cmd: 'real'
                    };

                else if (len == '04')
                    return {
                        error: 'real-' + data.substr(0, 2),
                        cmd: 'real',
                        errorCode: data.substr(0, 2)
                    };

                else if (len == '0A')
                    return {
                        error: false,
                        cmd: 'real',
                        readRate: parseInt(data.substr(2, 2), 16),
                        totalRead: parseInt(data.substr(4, 4), 16)
                    };

                else {
                    let retorno = {
                        error: false,
                        cmd: 'epc',
                        frequency: (parseInt(data.substr(0, 2), 16) & 0b11111100).toString(2), // 6bits
                        antena: (parseInt(data.substr(0, 2), 16) & 0b11) + 1, // Saco los últimos 2 bits y lo paso a decimal
                        pc: data.substr(2, 4)
                    };
                    switch (retorno.pc) {
                        case '1400':
                            retorno.epc = data.substr(6, 8);
                            break;
                        case '1800':
                            retorno.epc = data.substr(6, 12);
                            break;
                        case '3000':
                        case '3400':
                            retorno.epc = data.substr(6, 24);
                            break;
                        default:
                            retorno.error = 'pc-' + retorno.pc;
                            break;
                    }
                    return retorno;
                }

            case '8A': // cmd_fast_switch_ant_inventory
                if (len == '05' && data.substr(2, 2) == '22')
                    return {
                        error: 'antena-missing',
                        cmd: 'fast',
                        antena: parseInt(data.substr(0, 2), 16) + 1
                    };

                else if (len == '04')
                    return {
                        error: 'fast-' + data.substr(0, 2),
                        cmd: 'fast',
                        errorCode: data.substr(0, 2)
                    };


                else if (len == '0A')
                    return {
                        error: false,
                        cmd: 'fast',
                        readRate: data.substr(0, 3),
                        totalRead: data.substr(3, 4)
                    };

                else {
                    let retorno = {
                        error: false,
                        cmd: 'epc',
                        frequency: (parseInt(data.substr(0, 2), 16) & 0b11111100).toString(2), // 6bits
                        antena: (parseInt(data.substr(0, 2), 16) & 0b11) + 1, // Saco los últimos 2 bits y lo paso a decimal
                        pc: data.substr(2, 4)
                    };
                    switch (retorno.pc) {
                        case '1400':
                            retorno.epc = data.substr(6, 8);
                            retorno.rssi = data.substr(14, 2);
                            break;
                        case '1800':
                            retorno.epc = data.substr(6, 12);
                            retorno.rssi = data.substr(18, 2);
                            break;
                        case '3000':
                        case '3400':
                            retorno.epc = data.substr(6, 24);
                            retorno.rssi = data.substr(30, 2);
                            break;
                        default:
                            retorno.error = 'pc-' + retorno.pc;
                            break;
                    }
                    if (window.firmware == '08.02') { // If Version 8.2 permite 8 antenas, y hay que analizar el primer bit en binario del campo retorno.rssi para si la antena id es del 1 al 4 o del 5 al 8
                        // converitir a binario rssi
                        let rssiBin = parseInt(retorno.rssi, 16).toString(2);
                        // completar con ceros a la izquierda
                        rssiBin = '00000000'.substr(rssiBin.length) + rssiBin;
                        // si el primer bit es 1, la antena es del 5 al 8
                        if (rssiBin.substr(0, 1) == '1')
                            retorno.antena += 4;
                    }
                    return retorno;
                }
        }

    },

    splitHex: function (dataHex) {

        let hexs = [];
        let j = 0;
        let len = 0;

        do {
            if (dataHex.substr(j, 2) != 'A0') {
                return hexs;
            }

            len = parseInt(dataHex.substr(j + 2, 2), 16) * 2;
            hexs.push(dataHex.substr(j, len + 4));
            j += len + 4;

        } while (dataHex.length >= j)

        return hexs;

    },

    checkSum: function (buff) {
        let buffLen = buff.length;
        let uSum = 0;
        for (let i = 0; i < buffLen; i += 2) {
            uSum += parseInt(buff.substr(i, 2), 16);
        }
        uSum = (~uSum) + 1; // NOT Operator, bitwise negation
        uSum &= 0xFF; // Set Hex
        uSum = uSum.toString(16).toUpperCase();
        return (uSum.length == 1 ? '0' : '') + uSum;
    }

}