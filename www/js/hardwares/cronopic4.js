
var cronopic4 = {

    'config': {
        'enter': true,
        'baudios': 9600,
        'puerto': false
    },

    'serialport': null,
    tiempomuerto: 500,
    ultimo_corte: 0,

    conectar: function () {
        var that = this;
        var fotocelulas = [];

        alerta("Conectando Fotocélula Bluetooth ...");

        var listPorts = function() {
            // list the available BT ports:
            bluetoothSerial.list(
                function(devices) {
                    devices.forEach(function(device) {
                        if (device.name.toLowerCase().substring(0,8) == 'cronopic') {
                            fotocelulas.push(device);
                            return true;
                        }
                    })

                    if (fotocelulas.length > 1) {
                        alerta('Varias Fotocélulas configuradas');

                    } else if (fotocelulas.length == 1) {
                        bluetoothSerial.connect(fotocelulas[0].id, that.connectSuccess, that.connectFailure);

                    } else {
                        alerta('No se encontró una Fotocélula Bluetooth');
                    }

                },
                function(error) {
                    alerta(JSON.stringify(error));
                }
            );
        }

        // if isEnabled returns failure, this function is called:
        var notEnabled = function() {
            alerta("Bluetooth no está encendido");
        }

        // check if Bluetooth is on:
        bluetoothSerial.isEnabled(
            listPorts,
            notEnabled
        );

        return true;
    },

    desconectar: function () {
        bluetoothSerial.isConnected(
            function() {
                bluetoothSerial.unsubscribeRawData(
                    function () { alerta('Fotocélula Bluetooth desconectada')},
                    function () { console.log('Error al desconectar Fotocélula Bluetooth')}
                );
            },
            function() {
                console.log("Bluetooth is *not* connected");
            }
        );
    },

    // No logré que el subscribeRawData tomara este método
    // recibir: function (data) {

    //     if (!this.validar(data))
    //         return false;

    //     var lectura = $(".crono-esperando").last().parent();
    //     if (this.enter == 'esperar' && lectura.length)
    //         app.esperando(lectura);

    //     else
    //         app.crono();
    // },

    validar: function (data) {
        if (data[0] == 114) // r minúsculas; TODO: implementar con el 82 R mayúscula que es el control
            return true;
        else
            return false;
    },

    connectSuccess: function () {
        var that = this;
        bluetoothSerial.subscribeRawData(success, failure),
        alerta('Fotocélula Bluetooth conectado');
    },

    connectFailure: function () {
       alerta('Error al conectarse a la fotocélula Bluetooth');
    },

    validarTiempoMuerto: function () {
        var now = Date.now();

        if ((now - this.ultimo_corte) < this.tiempomuerto) {
            return false;
        }

        this.ultimo_corte = now;
        return true;
    }

}

function success(data) {

    var bytes = new Uint8Array(data);

    if (!cronopic4.validar(bytes))
        return false;

    if (!cronopic4.validarTiempoMuerto())
        return false;

    var lectura = $(".esperando").last();

    if (lectura.length)
        app.esperando(lectura);
    else
        app.crono();

}

function failure () {
    alerta('Error al suscribirse a la fotocélula Bluetooth');
}
