Mover stock masivamente

0) Exportar tabla stock

1) Selecciono todo lo que está en el depósito que muere
SELECT idproducto, stockactual FROM stock WHERE iddeposito = 2 AND stockactual != 0 ORDER BY idproducto;

2) Abrir planilla y agregar
CUIDADO CON EL PUNTO O LA COMA QUE EL EXCEL TRANSFORMA 1.000 EN 1000
=CONCAT("UPDATE stock SET stockactual = stockactual + ";B1;" WHERE iddeposito = 1 AND idproducto = ";A1;";")

3) Exportar como sql y ejecutar

4) Eliminar segundo depósito (opcional) y/o deshabilitar
DELETE FROM stock WHERE iddeposito = 2;
DELETE FROM depositos WHERE iddeposito = 2;
UPDATE categorias_ventas SET estado = 0 WHERE iddeposito = 2;

5) Revisar que haya quedado todo ok
