## ALIAS EN CRONO

```bash
alias art="php artisan"
alias tin="php artisan tinker"

alias crono="cd /var/www/cronometrajeinstantaneo/"
alias admin="cd /var/www/cronometrajeinstantaneo/admin"
alias code="cd /var/www/cronometrajeinstantaneo/code"
alias descargas="cd /var/www/cronometrajeinstantaneo/descargas"
alias db="sudo mysql -uroot -pFVB8VpZSjMCCataKXCYYXqgqC4q6KtQz"

alias rp="sudo service php8.1-fpm restart"
alias rm="sudo service mysql restart"
alias r="rp && rm"
alias kill_php="ps -eo pcpu,pid,args --sort=-pcpu | awk '/php-fpm/ && \$1 > 20 {system(\"sudo kill -9 \" \$2); echo \"Killed \" \$2 \" with \" \$1 \" CPU\"}'"
alias backup="mysqldump -uroot -pFVB8VpZSjMCCataKXCYYXqgqC4q6KtQz cronometrajeinstantaneo | gzip > /home/<USER>/backups/backup.sql.gz"
alias l="ls -la"
alias nano="echo 'Mejor usa mcedit con e'"
alias e="sudo mcedit"
alias t1="tail -f /var/www/cronometrajeinstantaneo/code/logs/$(date "+%Y-%m-%d")_resultados.csv"
alias t2="htop"
alias t4="tail -f /var/www/cronometrajeinstantaneo/code/logs/$(date "+%Y-%m-%d")_api.csv"
alias t3="tail -f /var/www/cronometrajeinstantaneo/code/logs/$(date "+%Y-%m-%d")_output_time.csv"
```

## ALIAS EN SAAS

```bash
# User specific aliases and functions
alias l="ls -lha"
alias wsfe="cd /saas/customer/services/acc/empresas/wsfe/"
alias rece="sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py"
alias antiafip="sudo php -f /saas/customer/services/scripts/crontab/antiafip.php"
alias saas="cd /saas/customer/services"
alias acc="cd /saas/customer/services/acc"
alias api="cd /saas/customer/services/api"
alias app="cd /saas/customer/services/app"
alias informes="cd /saas/customer/services/informes"
alias login="cd /saas/customer/services/login"
alias scripts="cd /saas/customer/services/scripts"
alias www="cd /saas/customer/services/www"
alias e="sudo mcedit"
alias h="htop"
alias kill_php="ps -eo pcpu,pid,args --sort=-pcpu | awk '/php-fpm/ && \$1 > 20 {system(\"sudo kill -9 \" \$2); echo \"Killed \" \$2 \" with \" \$1 \" CPU\"}'"
alias db="mysql -hdb.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH"
alias db-beta="mysql -hdb-beta.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH"

function deploy () {
  sudo /saas/customer/services/acc/command.php alfa migrate -yes
  sudo /saas/customer/services/acc/command.php beta migrate -yes
}

function consultando () {
l /saas/customer/services/acc/empresas/wsfe/*.consultando
l /saas/customer/services/acc/archivos/*.consultando_api
l /saas/customer/services/acc/archivos/*.importando
l /saas/customer/services/acc/archivos/*.informando
}
```