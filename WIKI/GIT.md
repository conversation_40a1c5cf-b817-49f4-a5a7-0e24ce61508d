# COMANDS Y PROCESOS CON GIT

## RECORDAR COMANDOS

git branch --no-merged
git branch --merged
git checkout -b funcionalidad_666 origin/funcionalidad_666
git push origin :funcionalidad_666
git reset --hard ORIG_HEAD
git restore <filename>
git branch -m <oldname> <newname>
git stash branch <branchname> [<stash>]
git merge --abort
git branch -a (muestra incluyendo remotos)
git fetch -p origin (fetch actualizando cache local)


## DEPLOY CON PUSH

mkdir beta
cd beta
git init --bare
nano hooks/post-receive
echo "#!/bin/sh" >> hooks/post-receive
echo "GIT_WORK_TREE=/saas/customer/services/beta git checkout -f" >> hooks/post-receive
chmod 755 hooks/post-receive
sudo chown -R apache:ec2-user /saas/customer/services/beta

git remote add beta ec2-user@18.231.86.55:/home/<USER>/gits/beta


## DESARROLLO EN origin

git checkout desarrollo
    Switched to branch 'desarrollo'
git checkout -b 666
    Crear branch 666 and switched to this branch
git push origin 666:666
    Subo a origin la nueva rama

--EL PROGRAMADOR DESIGNADO COMMITEA, HACE UN PUSH Y ME AVISA--

git fetch origin 666:666
    Traigo el trabajo realizado

--REVISO EL NUEVO CÓDIGO Y MODIFICO LO QUE HAGA FALTA--

git checkout desarrollo
    Switched to branch 'desarrollo'
git merge --no-ff 666
    Updating ea1b82a..05e9557
git branch -d 666
    Deleted branch 666 (was 05e9557)
git pull origin --delete 2834
    Deleted branch 666 on origin
git push origin desarrollo:master
    Actualizo rama en origin

git checkout beta
    Switched to branch 'beta'
git merge --no-ff desarrollo
    Updating ea1b82a..05e9557
git tag -a 1.2-b1
    Tag a la versión correspondiente
git push beta beta:master
    Deploy on beta


## DESARROLLO EN VERSIONES

git checkout desarrollo
    Switched to branch 'desarrollo'
git checkout -b 666
    Crear branch 666 and switched to this branch

  --VARIOS DESARROLLOS CON CADA UNO SU COMMIT CORRESPONDIENTE--

git commit -a "666 - Se realizó lo siguiente que se puede copiar desde el servicio"
    Commit every step you take

git checkout desarrollo
    Switched to branch 'desarrollo'
git merge --no-ff 666
    Updating ea1b82a..05e9557
git branch -d 666
    Deleted branch 666 (was 05e9557)

git checkout beta
    Switched to branch 'beta'
git merge --no-ff desarrollo
    Updating ea1b82a..05e9557
git tag -a 1.2-b1
    Tag a la versión correspondiente
git push beta beta:master
    Deploy on beta

  --MENSAJES A BETA TESTERS Y PRUEBAS GENERALES--

git checkout master
    Switched to branch 'master'
git merge --no-ff beta
    Updating ea1b82a..05e9557
git tag -a 1.2
    Tag a la versión correspondiente
git push produccion master
    Deploy on produccion

git diff beta
    Para ver si hay diferencias entre beta y master
git checkout beta
git merge --no-ff master
git push beta beta:master
    Deploy on beta con los errores que estaban en master también


## DESARROLLO EN ERRORES

git checkout master
    Switched to branch 'master'
git checkout -b error_666
    Crear branch error_666 and switched to this branch
git commit -am "error_666 - Error que hacía esa cosa mal"
    Commit every step you take
git checkout master
    Switched to branch 'master'
git merge --no-ff error_666
    Updating ea1b82a..05e9557
git tag -a 1.2.1
    Tag a la versión correspondiente con el de modificación menor
git branch -d error_666
    Deleted branch error_666 (was 05e9557)
git push origin master
    Deploy on origin


## ROLLBACK O VOLVER ATRÁS

Hay 2 formas principales: revert y reset

- `revert` para mantener el historial de commits (lo cual es recomendable si ya has compartido tu trabajo con otros) con `git revert <commit-hash>`
- `reset` para eliminar el commit de la historia completamente (esto no se recomienda si ya has compartido tu trabajo con otros). A su vez tiene estas opciones:
  - Modo suave (Mantiene los cambios en el área de staging) `git reset --soft HEAD~1`
  - Modo mixto (Deshace el commit y mantiene los cambios en el directorio de trabajo (no en staging)) `git reset --mixed HEAD~1`
  - Modo duro (Elimina el commit y todos los cambios asociados del directorio de trabajo) `git reset --hard HEAD~1`

Para apuntar a un commit se especifica con <commit-hash> y si se quiere ir pasos atrás del HEAD se hace con `HEAD~1` donde 1 es la cantidad de pasos
