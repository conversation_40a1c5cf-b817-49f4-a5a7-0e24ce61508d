Documentación: Procesamiento de Facturas con AWS Lambda
Descripción General

Este sistema utiliza AWS Lambda para procesar facturas electrónicas a través de la API de AFIPSDK. El flujo de trabajo es el siguiente:

- La aplicación principal envía un mensaje a una cola SQS con el formato `idempresa|idventa`
- La función Lambda afipsdk recibe el mensaje y procesa la factura
- La función Lambda obtiene los datos de la factura de la base de datos
- La función Lambda envía los datos a AFIP y recibe la respuesta
- La función Lambda actualiza el estado de la factura en la base de datos

Arquitectura

┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Aplicación │    │  Cola SQS   │    │   Lambda    │    │    AFIP     │
│  Principal  │───►│ afipsdk-queue│───►│   afipsdk   │───►│    API     │
└─────────────┘    └─────────────┘    └──────┬──────┘    └─────────────┘
                                             │
                                             ▼
                                      ┌─────────────┐
                                      │  Base de    │
                                      │   Datos     │
                                      └─────────────┘

Componentes
1. Cola SQS

    Nombre: afipsdk-queue-{stage} (donde {stage} es dev, alfa, beta o prod)
    URL: https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-{stage}
    Formato del mensaje: idempresa|idventa (ambos deben ser números enteros positivos)

2. Función Lambda

    Nombre: arca-{stage}-afipsdk
    Descripción: Procesador de facturas para AFIP
    Runtime: PHP 8.3
    Código fuente: services/lambda/arca/public/afipsdk.php
    Configuración: services/lambda/arca/serverless.yml

3. Base de Datos

    Se utiliza la clase Database de services/lambda/api/lib/Database.php
    Conecta a la base de datos de la empresa correspondiente
    Obtiene los datos de la factura y actualiza su estado

Flujo de Procesamiento

    Recepción del mensaje:
        La función Lambda recibe un mensaje de la cola SQS
        Valida que el mensaje tenga el formato correcto (idempresa|idventa)
    Obtención de datos:
        Conecta a la base de datos de la empresa
        Obtiene los datos de la venta y sus detalles
    Preparación de datos para AFIP:
        Formatea los datos según los requerimientos de la API de AFIP
        Genera el JSON para la solicitud
    Envío a AFIP:
        Envía los datos a la API de AFIP
        Recibe la respuesta (CAE, resultado, etc.)
    Actualización de la base de datos:
        Actualiza el estado de la factura con el resultado de AFIP
        Guarda el CAE y otros datos relevantes

Comandos Principales
Despliegue

# Navegar al directorio del proyecto
cd services/lambda/arca

# Desplegar en el entorno de desarrollo
serverless deploy --stage=dev

# Desplegar en otros entornos
serverless deploy --stage=alfa
serverless deploy --stage=beta
serverless deploy --stage=prod

Monitoreo y Logs

# Ver los logs de la función Lambda
serverless logs -f afipsdk

# Ver los logs en tiempo real
serverless logs -f afipsdk -t

# Ver los logs con AWS CLI
aws logs filter-log-events --log-group-name /aws/lambda/arca-dev-afipsdk --limit 20

Pruebas

# Enviar un mensaje de prueba a la cola SQS
aws sqs send-message --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --message-body "1234|5678"

# Invocar la función Lambda directamente (para pruebas)
serverless invoke -f afipsdk --data '{"Records":[{"body":"1234|5678"}]}'

# Probar la función localmente
serverless bref:local --function afipsdk --data '{"Records":[{"body":"1234|5678"}]}'

Gestión de la Cola SQS

# Ver atributos de la cola
aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --attribute-names All

# Ver mensajes en la cola
aws sqs receive-message --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --max-number-of-messages 10

# Purgar la cola (eliminar todos los mensajes)
aws sqs purge-queue --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev

Código de Ejemplo
Enviar un mensaje desde PHP

function afipsdk_lambda($idempresa, $idventa)
{
    try {
        $sqs = conectar_aws('sqs');

        $params = [
            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,
            'DelaySeconds' => 10,
            'MessageBody' => "$idempresa|$idventa",
        ];

        $sqs->sendMessage($params);

    } catch (Exception $e) {
        mostrar_error('No se envió un mensaje con SQS.<br>'
            .'Error: '.$e->getMessage().'<br>'
            .'Parametros: '.json_encode($params), true);
        return false;
    }
    return true;
}

Solución de Problemas
El Lambda no procesa los mensajes

    Verifica que la cola SQS tenga mensajes:

    aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --attribute-names ApproximateNumberOfMessages

    Verifica que el Lambda tenga los permisos correctos:

    aws lambda get-policy --function-name arca-dev-afipsdk

    Verifica que el mapeo de eventos esté configurado correctamente:

    aws lambda list-event-source-mappings --function-name arca-dev-afipsdk

Errores en el Lambda

    Revisa los logs para ver el error específico:

    serverless logs -f afipsdk

    Verifica que la conexión a la base de datos funcione correctamente
    Verifica que los datos de la factura sean correctos
    Verifica que la conexión con AFIP funcione correctamente

Próximos Pasos

    Implementar la integración real con la API de AFIP usando la librería afipsdk/afip.php
    Agregar manejo de errores más robusto
    Implementar reintentos para casos de fallo
    Agregar notificaciones para errores críticos
    Implementar monitoreo y alertas

Referencias

    Documentación de AWS Lambda
    Documentación de AWS SQS
    Documentación de Bref
    Documentación de AFIP