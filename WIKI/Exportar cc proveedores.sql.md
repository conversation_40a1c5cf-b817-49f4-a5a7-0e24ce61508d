SELECT
	c.idcompraxproveedor, p.nombre AS proveedor, fecha, idtipocompra,
	CASE idtipocompra
		WHEN 0 THEN 'Orden de pago'
		WHEN 1 THEN 'Factura'
		WHEN 2 THEN 'Remito'
		WHEN 3 THEN 'Nota de crédito'
		WHEN 4 THEN 'Nota de débito'
		WHEN 5 THEN 'Remito de devolución'
		WHEN 6 THEN 'Presupuesto'
		WHEN 7 THEN 'Pedido'
    END AS tipo,
	numero,
	CASE idtipocompra WHEN 0 THEN 0 ELSE total END AS deuda,
	CASE idtipocompra WHEN 0 THEN total ELSE 0 END AS pago,
	CASE idtipocompra WHEN 0
		THEN CONCAT('https://www.saasargentina.com/saas/compras.php?a=verpago&id=', id)
		ELSE CONCAT('https://www.saasargentina.com/saas/compras.php?a=ver&id=', id) END AS enlace
FROM comprasxproveedores AS c
   LEFT JOIN proveedores AS p ON c.idproveedor = p.idproveedor
ORDER BY c.idproveedor, fecha
