a2enmod headers

# Extra Security Headers
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header always append X-Frame-Options SAMEORIGIN
    Header set X-Content-Type-Options nosniff
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>

Content-Security-Policy: frame-ancestors 'none';


UPDATE wp_posts
SET campo = REPLACE(campo, 'https://cronometrajeinstantaneo.com', 'https://wp.cronometrajeinstantaneo.com')
WHERE campo LIKE '%https://cronometrajeinstantaneo.com%';
WordPress la concha de tu madre

## Configurar htpasswd

1 - Configurar el virtual host

```apache
<Directory "/var/www/simplementeviviendo/brain">
    Options FollowSymLinks MultiViews
    AllowOverride All
    Order allow,deny
    allow from all
	AuthType Basic
	AuthName "Sector privado"
    AuthUserFile /var/www/simplementeviviendo/brain/.htpasswd
    Require valid-user
</Directory>
```

2 - Des<PERSON><PERSON> crear el archivo de contraseña:

```bash
sudo htpasswd -c /var/www/simplementeviviendo/brain/.htpasswd andresmaiden
```

3 - Agregar el subdominio a Letsencrypt

```bash
sudo letsencrypt certonly --apache -d simplementeviviendo.com -d brain.simplementeviviendo.com
```

4 - Comprobar que todo esté bien y reiniciar Apache2

```bash
sudo apache2ctl configtest
sudo systemctl restart apache2
```