
#******************************************************************************
# PARA SYNC DOWN NUBES:
#******************************************************************************
prod_crono
cd /var/www/cronometrajeinstantaneo/www/descargas
rm cronometrajeinstantaneo.sql
mysqldump -uroot -pFVB8VpZSjMCCataKXCYYXqgqC4q6KtQz --databases cronometrajeinstantaneo > cronometrajeinstantaneo.sql
su pi
cd home/pi
wget --no-check-certificate https://cronometrajeinstantaneo.com/descargas/cronometrajeinstantaneo.sql
rm cronometrajeinstantaneo.sql

echo 1497 >> idevento
cat idevento
sudo mysqldump -uroot -p8des4rollo --databases cronometrajeinstantaneo > cronometrajeinstantaneo-736.sql
sudo mysql -uroot -p


DROP DATABASE cronometrajeinstantaneo;
CREATE DATABASE cronometrajeinstantaneo;
USE cronometrajeinstantaneo;
SOURCE cronometrajeinstantaneo.sql;

DELETE FROM participantes WHERE idevento NOT IN (1497);
DELETE FROM datosxparticipantes WHERE idevento NOT IN (1497);
DELETE FROM datosxeventos WHERE idevento NOT IN (1497);

DELETE FROM organizaciones WHERE idorganizacion NOT IN (SELECT idorganizacion FROM eventos WHERE idevento IN (1497));
DELETE FROM eventos WHERE idevento NOT IN (1497);
DELETE FROM carreras WHERE idevento NOT IN (1497);
DELETE FROM categorias WHERE idevento NOT IN (1497);
DELETE FROM etapas WHERE idevento NOT IN (1497);
DELETE FROM controles WHERE idevento NOT IN (1497);

DELETE FROM config_cronometraje WHERE idevento NOT IN (1497);
DELETE FROM config_organizacion WHERE idevento NOT IN (1497);
DELETE FROM config_vivo WHERE idevento NOT IN (1497);

TRUNCATE lecturas;
TRUNCATE penas;
ALTER TABLE `lecturas` DROP INDEX `uuid`;

ALTER TABLE `participantes` CHANGE `estado_carrera` `race_state` ENUM('inrace','hooked','lap','dnf','dsq','dns','otro') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inrace';

ALTER TABLE `participantes` CHANGE `estado_carrera_otro` `race_state_custom` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

ALTER TABLE eventos
ADD `sync_apps` tinyint(1) NOT NULL DEFAULT 1,
ADD `tiene_cronometraje` tinyint(1) NOT NULL DEFAULT 1,
ADD `tiene_milisegundos` tinyint(1) NOT NULL DEFAULT 1,
ADD `solo_segundos` tinyint(1) NOT NULL DEFAULT 1,
ADD `equipos` tinyint(1) NOT NULL DEFAULT 1,
ADD `cambios_de_tiempos` tinyint(1) NOT NULL DEFAULT 1,

ADD `tipolargada` enum('unica_largada','unica_largada_con_etapas','carreras_con_largadas','etapas_con_largadas','carreras_con_largadas_con_etapas','largadas_individuales','largadas_individuales_con_etapas','vueltas_fijas','etapas_con_largadas_vueltas','largadas_individuales_con_vueltas','largadas_individuales_con_etapas_continuas','') NOT NULL DEFAULT 'unica_largada',
ADD `tipo_largada` enum('evento','carreras','etapas','participantes','participantes_etapas') NOT NULL DEFAULT 'evento',
ADD `precision` enum('minutos','segundos','decisegundos','centisegundos','milisegundos','solo-minutos','solo-segundos') NOT NULL DEFAULT 'segundos',
ADD `ticket` tinyint(1) NOT NULL DEFAULT 1,
ADD `tiempo_vuelta` int(10) UNSIGNED NOT NULL DEFAULT 30,
ADD `penas` tinyint(1) NOT NULL DEFAULT 0,
ADD `penas_en_minutos` tinyint(4) NOT NULL DEFAULT 1,
ADD `limitar_penas` varchar(60) NOT NULL,
ADD `unificar_penas` tinyint(1) NOT NULL DEFAULT 1,
ADD `ordenar_ultima_etapa` tinyint(1) NOT NULL DEFAULT 0,
ADD `estado_carrera` tinyint(1) NOT NULL DEFAULT 1,
ADD `parciales` tinyint(1) NOT NULL DEFAULT 0,
ADD `minuto_cerrado` tinyint(1) NOT NULL DEFAULT 0,
ADD `no_oficial` tinyint(1) NOT NULL DEFAULT 0,
ADD `podios` int(10) UNSIGNED NOT NULL DEFAULT 3,
ADD `texto_resultados` varchar(191) NOT NULL,
ADD `abiertas` tinyint(1) NOT NULL DEFAULT 0,
ADD `multi_idparticipante` tinyint(1) NOT NULL DEFAULT 0,
ADD `multi_categoria` tinyint(1) NOT NULL DEFAULT 0,
ADD `equipo_participantes` varchar(191) NOT NULL DEFAULT '',
ADD `auto_numeracion` tinyint(1) NOT NULL DEFAULT 0,
ADD `auto_mail` tinyint(1) NOT NULL DEFAULT 0,
ADD `estado_predeterminado` enum('preinscripto','inscripto','acreditado','abandono','descalificado','anulado') NOT NULL DEFAULT 'preinscripto',
ADD `tipo_nombre` enum('libre','Nombre Apellido','Apellido, Nombre','APELLIDO Nombre') NOT NULL DEFAULT 'libre';

UPDATE organizaciones SET pass = 'd61f29328cbd43d836ef31aefb7236fd' WHERE idorganizacion = 335;


# ARMAR LISTA DE NUBES Y LAS FECHAS DE ACTUALIZADAS


# PARA NUBES NUEVAS:
# Agregar este código al apache
<Directory /var/www/www_encoded/public/>
    Options Indexes FollowSymLinks MultiViews
    AllowOverride All
    Order allow,deny
    allow from all
</Directory>

# Agregar al crontab
* * * * * ( sleep 00 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 01 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 02 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 03 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 04 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 05 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 06 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 07 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 08 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 09 ; /var/www/www_encoded/cache/cache.php )

* * * * * ( sleep 10 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 11 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 12 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 13 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 14 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 15 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 16 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 17 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 18 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 19 ; /var/www/www_encoded/cache/cache.php )

* * * * * ( sleep 20 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 21 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 22 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 23 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 24 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 25 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 26 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 27 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 28 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 29 ; /var/www/www_encoded/cache/cache.php )

* * * * * ( sleep 30 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 31 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 32 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 33 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 34 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 35 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 36 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 37 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 38 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 39 ; /var/www/www_encoded/cache/cache.php )

* * * * * ( sleep 40 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 41 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 42 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 43 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 44 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 45 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 46 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 47 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 48 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 49 ; /var/www/www_encoded/cache/cache.php )

* * * * * ( sleep 50 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 51 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 52 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 53 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 54 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 55 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 56 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 57 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 58 ; /var/www/www_encoded/cache/cache.php )
* * * * * ( sleep 59 ; /var/www/www_encoded/cache/cache.php )

# Actualizar software
crono_encoded # y seguir los pasos
wget https://cronometrajeinstantaneo.com/descargas/www_encoded_lxxq.tar.gz
tar -zxvf www_encoded_lxxq.tar.gz

rm -rf /var/www/old_www_encoded/
sudo mv /var/www/{,old_}www_encoded
sudo mv www_encoded /var/www

# Configurar permisos de Laravel
chmod -R 777 /var/www/www_encoded/storage
chmod -R 777 /var/www/www_encoded/bootstrap/cache
chmod -R 777 /var/www/www_encoded/cache

# Actualizar la app con .lan revisando .env y cronometraje.env
sed -i "s/cronometrajeinstantaneo.des/cronometrajeinstantaneo.lan/g" /var/www/www_encoded/.env
sed -i "s/yosoyroot/cronometrajeinstantaneo/g" /var/www/www_encoded/.env
sed -i "s/\/home\/<USER>\/www\/cronometrajeinstantaneo/\/var\/www\/www_encoded/g" /var/www/www_encoded/.env

sed -i "s/cronometrajeinstantaneo.des/cronometrajeinstantaneo.lan/g" /var/www/www_encoded/cronometrajeinstantaneo.env
sed -i "s/yosoyroot/cronometrajeinstantaneo/g" /var/www/www_encoded/cronometrajeinstantaneo.env
sed -i "s/\/home\/<USER>\/www\/cronometrajeinstantaneo/\/var\/www\/www_encoded/g" /var/www/www_encoded/cronometrajeinstantaneo.env

# Clear Laravel's Cache
php /var/www/www_encoded/artisan cache:clear
php /var/www/www_encoded/artisan config:clear
php /var/www/www_encoded/artisan view:clear

# Actualizar la base de datos
# Synd down


# Si se rompen las tablas en la nube híbrida

mysqlcheck --repair --all-databases / mysqlcheck -uroot -p --repair --all-databases


#*******************************************************************************
# PARA SYNC UP NUBES:
#*******************************************************************************
(Otra posible conexión: ftp -p lumapatagonia.com.ar luma itrmnvF254Cs)

sudo mysqldump -uroot -p8des4rollo --databases cronometrajeinstantaneo > cronometrajeinstantaneo-388.sql
ftp -p ftp.patagoniainfinita.com
infinita
jB254vx134
ls
put cronometrajeinstantaneo-388.sql
get cronometrajeinstantaneo-388.sql
delete cronometrajeinstantaneo-388.sql

# Exportos las tablas en la nube
SELECT * FROM eventos WHERE idevento = 388;

# Borro online el evento y lo importo manualmente
DELETE FROM eventos WHERE idevento = 388;
# ya no funciona INSERT INTO `eventos` (`idevento`, `idorganizacion`, `nombre`, `codigo`, `inscripciones`, `resultados`, `cronometraje`, `mail`, `fecha`, `localidad`, `tipolargada`, `equipos`, `cambios_de_tiempos`, `tiene_milisegundos`, `largada`, `terminada`, `inscripciones_estilo`, `inscripciones_texto`, `inscripciones_js`, `inscripciones_preinscripto`, `resultados_estilo`, `resultados_con_estilo`, `resultados_filtros`, `resultados_buscador`, `auto_numeracion`, `auto_mail`, `auto_refresh`, `mail_preinscripto`, `mail_inscripto`, `etapas`, `precio`, `pago`, `multicarrera`) VALUES
(388, 41, 'Copa Nariño de Downhill 2019 Válida 1 Jojoa', 'copa-narino-de-downhill-2019-valida-1-jojoa', 0, 1, 1, '', '2019-05-19', 'Pasto', 'largadas_individuales_con_etapas', 0, 1, 1, '2019-05-19 09:50:16.268', '0000-00-00 00:00:00.000', '', '', '', '', '', 1, 0, 0, 0, 0, 0, '', '', 0, '100.00', 0, 0);

# Verificar manualmente las carreras, etapas y controles
SELECT * FROM carreras WHERE idevento = 388;
SELECT * FROM categorias WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 388);
SELECT * FROM etapas WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 388);
SELECT * FROM datosxeventos WHERE idevento = 388;

# Conocer cuál es el primer y último idinscripcion de la nube y de online
SELECT idinscripcion FROM participantes WHERE idevento = 388 ORDER BY idinscripcion ASC LIMIT 1;
SELECT idinscripcion FROM participantes WHERE idevento = 388 ORDER BY idinscripcion DESC LIMIT 1;
SELECT idinscripcion FROM participantes ORDER BY idinscripcion DESC LIMIT 1;

# Calcular la diferencia y sumarla en la nube
UPDATE participantes SET idinscripcion = idinscripcion + 700 WHERE idevento = 388;
UPDATE datosxparticipantes SET idinscripcion = idinscripcion + 700 WHERE idevento = 388;

# Exporto las tablas en la nube
SELECT * FROM participantes WHERE idevento = 388;
SELECT * FROM datosxparticipantes WHERE idevento = 388;

# Borro online los datos y los importo con los archivos
DELETE FROM participantes WHERE idevento = 388;
DELETE FROM datosxparticipantes WHERE idevento = 388;

# Hacer lo mismo con lecturas
SELECT * FROM lecturas WHERE idcontrol IN
(SELECT idcontrol FROM controles WHERE idetapa IN
(SELECT idetapa FROM etapas WHERE idcarrera IN
(SELECT idcarrera FROM carreras WHERE idevento = 388)));
