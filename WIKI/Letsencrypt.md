sudo certbot --apache
# sudo letsencrypt certonly --apache -d cronometrajeinstantaneo.com -d www.cronometrajeinstantaneo.com -d admin.cronometrajeinstantaneo.com -d app.cronometrajeinstantaneo.com -d wp.cronometrajeinstantaneo.com -d beta.cronometrajeinstantaneo.com
# sudo letsencrypt renew --force-renew --manual-public-ip-logging-ok

# sudo letsencrypt certonly --apache -d agente.ar -d www.agente.ar -d n8n.agente.ar -d app.agente.ar

USAR NUEVO PLUGIN PARA WP
WP Free SSL – Free SSL Certificate for WordPress and force HTTPS

NO USAR MÁS - RENOVAR EN WP ENCRPYT
- Visitar el plugin con &includewww=1 atrás
https://nodomateriales.com/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1
- <PERSON><PERSON> y descargar los 2 archivos de validación
- Ir a la descarga y subir directo a la carpeta online (ojo que agrega txt y lo sacamos acá) Qu9jfvVrU7YW
scp nombre-archivo-validacion.txt <EMAIL>:/home/<USER>/public_html/.well-known/acme-challenge/nombre-archivo-validacion
- Verify Challenge y descargar cert y key
- Ir a http://vps-1723455-x.dattaweb.com:2083/#/domain/ssl en el dominio correcto e Instalar Nuevo

https://nodomateriales.com/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1
https://quetrihueviajesyturismo.tur.ar/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1
https://simplementeviviendo.com/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1


EN NODO DESACTIVAR EL PLUGIN DEL PDF PARA QUE FUNCIONE