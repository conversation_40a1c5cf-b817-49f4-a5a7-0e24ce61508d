UPDATE bienes SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO';
UPDATE servicios SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO';
UPDATE ventas SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO';
UPDATE ventaspagos SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO';
UPDATE ventasxclientes SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO';


UPDATE bienes SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO';
UPDATE servicios SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO' AND fechainicio > 'FECHA_DESDE';
UPDATE ventas SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO' AND fecha > 'FECHA_DESDE';
UPDATE ventaspagos SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO' AND fecha > 'FECHA_DESDE';
UPDATE ventasxclientes SET idcliente = 'CLIENTE_NUEVO' WHERE idcliente = 'CLIENTE_VIEJO' AND fecha > 'FECHA_DESDE';



UPDATE compras SET idproveedor = 'PROVEEDOR_NUEVO' WHERE idproveedor = 'PROVEEDOR_VIEJO';
UPDATE compraspagos SET idproveedor = 'PROVEEDOR_NUEVO' WHERE idproveedor = 'PROVEEDOR_VIEJO';
UPDATE comprasxproveedores SET idproveedor = 'PROVEEDOR_NUEVO' WHERE idproveedor = 'PROVEEDOR_VIEJO';


UPDATE compras SET idproveedor = 'PROVEEDOR_NUEVO' WHERE idproveedor = 'PROVEEDOR_VIEJO' AND fecha > 'FECHA_DESDE';
UPDATE compraspagos SET idproveedor = 'PROVEEDOR_NUEVO' WHERE idproveedor = 'PROVEEDOR_VIEJO' AND fecha > 'FECHA_DESDE';
UPDATE comprasxproveedores SET idproveedor = 'PROVEEDOR_NUEVO' WHERE idproveedor = 'PROVEEDOR_VIEJO' AND fecha > 'FECHA_DESDE';
