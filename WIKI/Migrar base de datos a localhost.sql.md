#*******************************************************************************
# Si es una base de datos común
#*******************************************************************************

# EXPORTAR la base de datos y cerrar la pestaña por las dudas

# Buscar y reemplazar MD5 por 'Aca lo uso en local saas_325'
GRANT ALL PRIVILEGES ON saas_325.* TO 'saas_325'@'localhost' IDENTIFIED BY '6bf445f80ea63c7e5117f7c54c9fda57';
FLUSH PRIVILEGES;

# Cambio la empresa al servidor 10 que es desarrollo
UPDATE `saasargentina`.`empresas` SET `idservidor` = '10', `estado` = 'activada' WHERE `idempresa` = '325';

# Si es una empresa que ya está en localhost
UPDATE `saasargentina`.`usuarios` SET `mail` = 'idempresa_325', `pass` = '402051f4be0cc3aad33bcf3ac3d6532b', `estado` = 1 WHERE `idempresa` = '325' LIMIT 1;

# IMPORTAR la base de datos
CREATE DATABASE saas_325;
USE saas_325;
source saas_325.sql;


#*******************************************************************************
# Si es una nueva o no está actualizada
#*******************************************************************************

INSERT INTO `saasargentina`.`empresas` (`idempresa`, `nombre`, `iue`, `mail`, `idcliente`, `idservidor`, `idsistema`, `estado`, `tienda`, `fechaalta`, `fechacontinuidad`, `random`, `ultimocontacto`, `observacion`) VALUES
(325, 'SISTEMAS MEDICOS LATINOAMERICA', 'ZVWyWhiEbisksPQWRpn9', '<EMAIL>', 0, 1, 1, 'prueba', '', '2013-11-30 01:42:45', '0000-00-00 00:00:00', '', 'encuesta', '');

INSERT INTO `saasargentina`.`usuarios` (`idusuario`, `iuu`, `estado`, `idempresa`, `mail`, `pass`, `fechabaja`, `random`, `tiposolicitado`, `solicitado`) VALUES
(1299, 'wzwNVzJbjFyh2T4QRNDj', 1, 325, '325', '402051f4be0cc3aad33bcf3ac3d6532b', NULL, '', '', '');


#*******************************************************************************
# Si quiero descargar saasargentina de nuevo
#*******************************************************************************

TRUNCATE `saasargentina`.`servidores`;
INSERT INTO `saasargentina`.`servidores` (`idservidor`, `nombre`, `version`, `BD_HOST`,  `BD_HOST_RO`, `BD_USER`, `BD_PASS`, `BD_BD`, `BD_PORT`, `BD_SOCKET`, `URL`) VALUES
(10, 'Desarrollo', '', '127.0.0.1', '127.0.0.1', 'saas_', 'Aca lo uso en local saas_', 'saas_', 0, 0, 'http://app.saasargentina.des/');

UPDATE `saasargentina`.`empresas` SET `estado` = 'eliminada';
UPDATE `saasargentina`.`empresas` SET `idservidor` = '7', `estado` = 'activada' WHERE `idempresa` IN (99,161,874,1548,4510,8666,6149,10706);

UPDATE `saasargentina`.`usuarios` SET `pass` = '402051f4be0cc3aad33bcf3ac3d6532b';
