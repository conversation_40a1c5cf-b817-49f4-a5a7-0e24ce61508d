RDS:
- Se puede cambiar la contraseña del root si te mandas una cagada como la mía. Se hace entrando al nodo y poniendo para editar la configuración. Ahí se puede poner una nueva contraseña. En la parte de información de configuración dice el usuario root que en nuestro caso es saasroot.
- El sql_mode tiene que ser: NO_AUTO_VALUE_ON_ZERO, NO_ZERO_DATE, NO_ZERO_IN_DATE
- Cambiamos los parámetros: ``max_execution_time = 60000`` , `ft_min_word_len = 3`, `time_zone = America\Montevideo` (en el cluster)
- Para probar si eso hace que se cuelgue le puse `sync_binlog = 0`, estaba en 1

-   Se puede entrar por ssh al nodo y de ahí entrar a la instancia de RDS con el comano `db` que es lo mismo que  `mysql -hdb.saasargentina.com -usaasroot -p` teniendo la contraseña del usuario `saasroot` que se puede ver con `cat /saas/customer/services/acc/acc.php`
-   Conectado al rds se pueden ver los procesos con `SHOW PROCESSLIST;`
-   Se pueden matar procesos (menos del usuario `rdsadmin`) con la query `CALL mysql.rds_kill(666);` si el process_id es 666
-   Para matar todos los procesos de ciertas instancias se puede hacer `SELECT CONTAC('CALL mysql.rds_kill(', id, ');') FROM information_schema.processlist WHERE user IN ('saas_6149', 'saas_7450', 'saas_874');`
-   Para matar todos los procesos de más de 60 segundos se puede hacer `SELECT CONTAC('CALL mysql.rds_kill(', id, ');') FROM information_schema.processlist WHERE TIME > 60`
-   Puede servir probar de cambiar `max_execution_time` pero aparentemente no es compatible con la versión. Para ver el valor actual `SHOW VARIABLES LIKE 'max_execution_time';`

Para arreglar una base de datos o todas:
mysqlcheck -hdb.saasargentina.com -usaasroot -pxxx --auto-repair -c -o saas_98
mysqlcheck -hdb.saasargentina.com -usaasroot -pxxx --auto-repair -c -o --all-databases

## REINICIAR RDS
sudo service php-fpm-7.1 restart
sudo service httpd-default restart
sudo shutdown -r now

Tengo el siguiente comando para eliminar los procesos de PHP que consumen más del 20% de la CPU:
```
alias k="ps -eo pcpu,pid,args --sort=-pcpu | awk '/php-fpm/ && \$1 > 20 {system(\"sudo kill -9 \" \$2); echo \"Killed \" \$2 \" with \" \$1 \" CPU\"}'"
```

OPSWORKS y CHEF:
- La configuración del apache está en /etc/httpd-default/conf.d
- La receta para la configuración de apache en www está en site_default.conf.erb y se deploya en 00-default.conf
- La receta para la configuración de apache de los otros services está en site.conf.erb y se deploya en app.conf
- Los certificados se cambian adentro de AWS en el administrador de CERTs (no se si también hay que cambiarlos en chef, por las dudas los cambié)
- Entrar a Instances y ver la IP de la instancia a la que quiero entrar (por ej. *************)
ssh -i ~/.ssh/desarrollo.pem ec2-user@************* (o ssh -oHostName=************* saas-prod)


CLOUDWATCH LOGS INSIGHT
fields @timestamp, @message
| filter empresa.idempresa = 6132 and id = 14519
| sort @timestamp desc
| limit 20

{ $.modulo = "productos" && $.a = "Modificar" && $.id = "7097" }

SHOW VARIABLES LIKE 'read_only';
SELECT @@hostname;

## REINICIAR EC2

En algunas oportunidades al reinicar el EC2, se desconfigura la instancia del Load Balancer. Para arreglarlo hay que:
- Entrar en EC2 > Load Balancers
- Seleccionar el Load Balancer e ir a Target Intances
- Ir a Manage Instances, agregar la nueva instancia de EC2 y darle Save changes



## CLOUDSEACH / REDIS / ELASTISEARCH / FULLSEARCH

- En el issue https://gitlab.com/saasargentina/app/-/issues/1619 traté de configurar cloudseach pero una vez que estaba avanzado ví que los precios podían ser muy altos, por lo tanto plantié estas opciones y este plan

1. Seguir con Fullsearch pero pasarlo a una indexación de 4 letras mínimo (así es el predeterminado y lo bajamos a 3 modificando la configuración de Mysql), eliminar todos los campos que no sean necesarios y separar el fullsearch de productos y clientes a tablas a parte. Esto es lo más rápido y sencillo y no sale nada extra.
2. Seguir con la opción de Cloudsearch pero tengo miedo que con las importaciones nos vayamos a varios miles de dólares por mes.
3. Probar de configurar un sistema de Elasticsearch/Redis en una instancia dedicada a mano. La verdad que nunca lo hice, sería arrancar de cero con esa tecnología y empezar a toparnos con cada uno de los problemas que uno se topa cuando arrancas con algo nuevo.
4. Buscar soluciones "Full Text Search as a Service", estoy viendo todas las de https://medevel.com/os-fulltext-search-solutions. No se si conseguiremos una a un precio razonable.
5. Existe un Redis Search en la que se puede guardar info en Redis y buscarla ahí. No se como funcionará, ni que tanto peso genere, pero lo bueno es que ya tenemos un Redis configurado para las memorias de sesión. O sea que podríamos guardar ahí las tablas fullseach y buscarlas ahí. En teoría funcionaría. En la práctica con el tamaño de las bases que tenemos puede colgarnos las variables de sesión y/o anda a saber que más.

Me voy por la opción 1, y dejando como plan B la opción 3 con Opensearch y plan C la opción 5 con el redis que ya tenemos.
Por ahora abandono opciones 2 y 4 por un tema de costos.


## RECUPERAR BACKUP RDS

Para recuperar un backup de RDS hay que hacer lo siguiente:

- Ir a la consola de AWS > entrar en RDS > Copias de seguridad automatizadas
- Seleccionar la base de datos que se quiere recuperar y en Instantáneas del sistema seleccionar el día que se quiere recuperar (VER BIEN QUE DÍA Y HORA SE QUIERE RECUPERAR)
- Ir a Acciones > Exportación a Amazon S3
- Completar los campos (por ej. para una tabla, pero pueden ser muchas o todas) con nombre `saas_666_precios_2024-01-01`, datos exportados Parcial `saas_666.precios` , bucket saasargentina-backups y prefijo `saas_666_precios_2024-01-01/`, rol y política de IAM `manual_exports_role`
- ESPERAR PORQUE TARDA UN RATO
- Accede a Amazon Athena > Editor de Consultas > Configuración > Administrar
- Utilizar la herramienta de Browse S3 para seleccionar el bucket y la carpeta donde se exportó el backup `saas_666_precios_2024-01-01/saas_666.precios/` y guardar
- Volver al editor y crear la base de datos `CREATE DATABASE saas_666;`
- Crear la/s tabla/s
```
CREATE EXTERNAL TABLE precios (
  idproducto int,
  idlista tinyint,
  utilidad decimal(6,2),
  precio decimal(15,2),
  preciofinal decimal(15,2)
)
STORED AS PARQUET
LOCATION 's3://saasargentina-backups/saas_666_precios_2024-01-01/saas_666.precios/';
```
- Ya se pueden ejecutar todas las queries necesarios y si es necesario exportarlas como CSV
