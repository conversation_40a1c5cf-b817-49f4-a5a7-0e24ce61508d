Para recuperar una empresa ya eliminada en SaaS, por ej saas_11633

- cd /saas/customer/services/acc/empresas/eliminadas/11633
- db

```
CREATE DATABASE saas_11633;
USE saas_11633;
SOURCE saas_11633.sql;

UPDATE empresas SET estado = 'activada', fechaeliminada = '0000-00-00', observacion = CONCAT('Empresa recuperada de eliminación el 2024-07-08', observacion) WHERE idempresa = '11633';
SELECT idcliente FROM empresas WHERE idempresa = '11633';

UPDATE saas_99.clientes SET estado = 1 WHERE idcliente = 3214;
```

- Los archivos, incluyendo wsfe se tienen que recuperar a mano
