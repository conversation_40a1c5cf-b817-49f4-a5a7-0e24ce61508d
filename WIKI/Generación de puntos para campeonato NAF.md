# GENERACIÓN DE PUNTOS

Para el campeonato NAF, se deben generar los puntos de los participantes. Para ello, se debe tener en cuenta que los puntos se generan por evento y por categoría. Estas son las consultas sql que fui utilizando.


**Para generar en el excel**

```sql
INSERT INTO datosxparticipantes SET iddato = 'puntos', idevento = 2075, dato = '', idinscripcion = '';

SELECT dni, idinscripcion FROM datosxparticipantes WHERE idevento = 2075 AND iddato = 'documento_numero' AND dato = '';
```

**Verifico que sean todos iddato = 'documento_numero'**

`SELECT * FROM datosxeventos WHERE idevento IN (1918,1932,1933,2075);`

**Obtengo los idinscripciones de los que no tienen dni**
```sql
SELECT idinscripcion FROM datosxparticipantes WHERE idevento IN (1918,1932,1933,2075) AND iddato = 'documento_numero' AND dato IN
(35489665, 39149235, 26271493, 44916647, 41551745, 28867028, 30488280, 65826805, 49604190, 43862944, 2007000, 35947295, 65840409, 42194742, 40817689, 36815316, 40770267, 28941688, 29761556, 62442917, 19759438, 36410982, 41533991, 37783879, 63054137, 46835285, 16594411, 44649642, 39708112, 33095593, 28437758, 31951187, 29981261, 19098357, 51790961, 51052866, 62442898, 30634158, 18866195, 38895061, 55980922, 34239881, 46604692, 51803560, 27687182, 656409509, 20231192, 56839063, 35442803, 47978674, 37018517, 16657930, 16528648, 33060851, 64699499, 48137645, 40130746, 14347574, 41847673, 57234608, 77779167, 49462220, 34994617, 51467221, 51132486, 41091474, 47855163, 34741600, 18783783, 47433268, 33919167, 64870548, 64707913, 18840349, 29190872, 39086879, 41079113, 34218351, 30937009, 30514843, 33148893, 49135886, 35173292, 34166676, 46721096, 16452102, 33939298, 25632470);
```

**Obtengo los datos de esas idinscripciones**
```sql
SELECT p.nombre, p.mail, categorias.nombre AS categoria, p.localidad, dato FROM participantes p
LEFT JOIN categorias ON p.idcategoria = categorias.idcategoria
LEFT JOIN datosxparticipantes ON p.idinscripcion = datosxparticipantes.idinscripcion AND iddato = 'documento_numero'
WHERE p.idinscripcion IN
(815286, 815294, 815298, 815299, 815300, 815304, 815305, 815306, 815310, 815311, 815314, 815317, 815318, 815322, 815326, 815327, 815329, 815331, 815332, 815335, 815336, 815337, 815341, 815344, 815346, 815348, 815350, 818845, 818847, 818850, 818851, 818854, 818857, 818858, 818862, 818863, 818864, 818866, 818867, 818869, 818870, 818871, 818875, 818879, 818882, 818883, 818884, 818893, 850318, 850319, 851012, 851984, 857568, 861721, 861725, 861914, 862789, 863670, 865681, 865828, 865829, 866151, 867001, 867068, 867073, 867290, 867439, 867450, 867451, 867463, 867477, 867644, 867654, 867674, 867700, 867724, 867754, 867874, 867904, 867910, 867921, 867923, 867927, 867929, 868089, 868097, 868111, 868169);
```

SELECT idinscripcion, dato FROM `participantes` WHERE idevento = 2075