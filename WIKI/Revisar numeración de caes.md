Revisar numeración de caes
// Cambiar idempresa 874
// Cambiar idtipoventa 28
// Cambiar numero_desde 24441
// Cambiar numero_hasta 24442


https://scripts.saasargentina.com/numeracion.php?a=cae&idempresa=874&idtipoventa=28&numero_desde=24441&numero_hasta=24442


https://scripts.saasargentina.com/numeracion.php?a=listar&idempresa=874&idtipoventa=28&numero_desde=24441&numero_hasta=24442
https://scripts.saasargentina.com/numeracion.php?a=insertar&idempresa=874&idtipoventa=28&numero=24441

https://scripts.saasargentina.com/numeracion.php?a=duplicar&idempresa=874&idtipoventa=28&idventa_desde=355203&numero=37052
// FALTAN COMPLETAR OTRAS FUNCIONES

// COMANDOS PARA REPARAR
SELECT * FROM saas_874.ventas WHERE idventa IN (26040);
UPDATE saas_874.ventas JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
SET fecha = NOW(), cae = '', numero = categorias_ventas.ultimonumero + 1
WHERE idventa IN (26040);

UPDATE saas_874.ventas SET cae = '70197891256362' WHERE idventa = 25620;
UPDATE saas_874.ventas SET numero = 20285, fecha = NOW() WHERE idventa = 25593;


SELECT
(SELECT cuit FROM saas_874.configuraciones LIMIT 1) AS cuit_emisor,
v.idventa, v.idtipoventa, cv.idcomportamiento, letra, puntodeventa, numero, cuit, dni, total, fecha
FROM saas_874.ventas AS v
JOIN saas_874.categorias_ventas AS cv ON cv.idtipoventa = v.idtipoventa
WHERE cae = '70199868953507';
SELECT * FROM saas_874.ventas WHERE cae = '70199868953507';

UPDATE saas_4325.ventas SET cae = '', fecha = NOW(), numero =  WHERE idventa IN ();
UPDATE saas_4325.ventas SET cae = '70191890829238', fecha = '2020-05-11', numero = 23826 WHERE idventa = 316092;
UPDATE saas_4325.ventas SET cae = '70191891388809' WHERE idventa = 316183;
UPDATE saas_4325.ventas SET estado = 'anulado' WHERE idventa = 25603;


UPDATE saas_4325.ventas SET estado = 'cerrado' WHERE idventa IN (35, 37, 39, 48, 57, 59);
UPDATE productos SET idiva = 1 WHERE idiva = 0;
UPDATE productosxventas SET idiva = 1 WHERE idiva = 0;
UPDATE productosxcompras SET idiva = 1 WHERE idiva = 0;

alias l="ls -lha"
alias wsfe="cd /saas/customer/services/acc/empresas/wsfe/"
alias rece="python /saas/customer/services/acc/tools/pyafipws/rece1.py"
alias antiafip="php -f /saas/customer/services/scripts/crontab/antiafip.php"

python /saas/customer/services/acc/tools/pyafipws/ws_sr_padron.py /saas/customer/services/acc/empresas/wsfe/30715446967.ini 20187874603 --constancia

python /saas/customer/services/acc/tools/pyafipws/rece1.py /saas/customer/services/acc/empresas/wsfe/27258678481.ini /ult 6 9
sudo php -f /saas/customer/services/scripts/crontab/antiafip.php
