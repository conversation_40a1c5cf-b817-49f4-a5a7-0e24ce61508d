## ACLARACIONES

- Para lambdas de empresas, generamos igual un registro de su tabla scripts
- En AWS Lambda usamos el framework https://bref.sh/ para deployar lambdas en PHP más fácilmente
- Define your application in a simple `serverless.yml` file and deploy with `serverless deploy`
- Tenemos 2 librerías para llamar a functions de Lambdas desde código ( `app/public/librerias/funciones_aws.php` y `api/funciones_api.php` ) que tienen instalados los SDK de AWS. Además se puede llamar desde cron
- Vamos a agregar todo lo que lleva queue en Lambdas y SQS

## PASOS PARA CREAR UNA LAMBDA

1. Crear una carpeta en el repo `lambda` con el nombre de la lambda, por ej `myFunction`
2. Entrar a la carpeta y comenzar un bref project con `composer require bref/bref`
3. Iniciar el proyecto con `vendor/bin/bref init`
4. Crear el .gitignore con `echo "vendor" > .gitignore; echo ".serverless" >> .gitignore`
5. Edita el archivo serverless.yml para configurar tu función Lambda. Asegúrate de especificar el `service: nombre_app`, `stage: dev`, `runtime: provided.al2`, `region: sa-east-1` y el handler correcto.
<!-- 6. Ir a *AWS > Lambda > Crear una función* con el nombre de la carpeta precedida por `app-dev-` -->
7. Deploy con `serverless deploy --stage=alfa`
8. Probar la function en local con `serverless bref:local --function fe --data '{"name": "Test"}` o en AWS pero desde mi consola con `serverless invoke -f myFunction`
9. Para usar variables de entorno con .env `composer require vlucas/phpdotenv`


## ME FALTA VER Y DOCUMENTAR

Lambda creará un rol de ejecución denominado app-dev-empresa_9589_script_1-role-4mnxfi2q, con permiso para cargar registros a Amazon CloudWatch Logs.
