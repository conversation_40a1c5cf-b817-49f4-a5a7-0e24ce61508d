# INTRODUCCIÓN

En este repositorio se unifican las apps de cronometrajeinstantaneo.com que pueden ser compiladas para todos los sistema operativos, utilizando volt.build para iOS y Android, y Electron para Windows y Linux.

## VOLT.BUILD

Este proceso actualiza iOS y Android

1. Configurar crono.js con la `PLATFORM` y la `VERSION` correcta
2. Modificar el archivo config.xml con la `version` correcta
3. Configurar el archivo voltbuilder.json con el `platform` y el `release` (debug/release) correcto (en caso de Android también `androidPackageType` como `apk` o `bundle`)
4. Compilar con volt.build en https://volt.build/upload/

## ELECTRON

### cronometrajeinstantaneo.app PARA INSTALAR
Hay que tener instalado en el equipo:
1. Instalar git https://git-scm.com/downloads y clonar repositorio
2. Instalar NodeJS y NPM
3. Instalar librerías: _npm install_
4. Iniciar la aplicación con _npm start_ y probarla
5. Compilarla con _npm run make_
6. Eliminar compilaci<C3><B3>n anterior con _rm .\out\cronometrajeinstantaneo-v2.10.0.exe_
7. Mover el archivo con _mv '.\out\make\squirrel.windows\x64\cronometrajeinstantaneo.app-2.10.0 Setup.exe' .\out\cronometrajeinstantaneo-v2.10.0.exe_  
8. Cambiar el <C3><AD>cono con _..\rcedit-x64.exe .\out\cronometrajeinstantaneo-v2.10.0.exe --set-icon .\archive\icon-isotipo.ico_
