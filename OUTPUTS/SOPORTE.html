<!DOCTYPE html>
<html>
<head>
<title>SOPORTE.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%F0%9F%A5%87-roads--crono--soporte">🏅 ROADS &gt; CRONO &gt; SOPORTE</h1>
<hr>
<h2 id="varios">VARIOS</h2>
<ul>
<li>
<p><input type="checkbox" id="checkbox0" checked="true"><label for="checkbox0">Habilitar Beta Multimoneda a Javi</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox1" checked="true"><label for="checkbox1">Recuperar tu tools</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox2" checked="true"><label for="checkbox2">Arreglar chips Kart Chile (anotar que debe septiembre)</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox3" checked="true"><label for="checkbox3">Aprobar pago Venezuela y comprar BTC</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox4" checked="true"><label for="checkbox4">Desactivar enlace Panamá</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox5" checked="true"><label for="checkbox5">Sacar texto Mendoza</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox6" checked="true"><label for="checkbox6">Feedback Gaby Género</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox7" checked="true"><label for="checkbox7">Responder Lona a Claudio</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox8"><label for="checkbox8">Procesar tus audios</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox9"><label for="checkbox9">Ver que terminas haciendo con Nequi</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox10"><label for="checkbox10">Configurar botones de pago Ride</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox11"><label for="checkbox11">SARR: Logos + Cat</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox12"><label for="checkbox12">Pedido Colombia</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox13"><label for="checkbox13">Vídeo explicativo Orozco</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox14"><label for="checkbox14">Reunion España rosfit</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox15"><label for="checkbox15">Factura 60 a Gabriela</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox16"><label for="checkbox16">Ordenar Todoist</label></p>
</li>
<li>
<p><input type="checkbox" id="checkbox17"><label for="checkbox17">Configurar botones Javi Ushuaia y ver presupuesto</label></p>
</li>
</ul>
<h2 id="ordenar">ORDENAR</h2>
<ul>
<li><input type="checkbox" id="checkbox18"><label for="checkbox18">Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)</label></li>
<li><input type="checkbox" id="checkbox19"><label for="checkbox19">Buscar pileta</label></li>
<li><input type="checkbox" id="checkbox20"><label for="checkbox20">Cortar árbol mañana</label></li>
<li><input type="checkbox" id="checkbox21"><label for="checkbox21">Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)</label></li>
</ul>
<h2 id="ahora">AHORA</h2>
<p>Medio de pago Mercadopago
Precio 50.000 (pre inscripción)
Carreras en las que se aplica (Open Shimano Enduro Las Vegas) O (campeonato Argentino Enduro
Cantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones) no hay limite cantidad.
Fecha y hora desde que se habilita este precio desde hoy
Fecha y hora hasta que se deshabilita este precio hasta el día 30/9 23:30hs</p>
<p>Y si se puede configurar que ese pago tenga una especie de ID mejor para despues filtrar y hacer las cuentas</p>
<hr>
<p>.sin_medalla, .nacionalidad_nombre, #informe_generales .sexo, #informe_categorias .sexo, #informe_etapas .sexo {
display: block;
}</p>
<p>.medalla {
display: none;
}</p>
<p>dale la factura al cuit de mi empresa 30-71745220-4 Bellas sur sas
Factura por $60000</p>
<ul>
<li><input type="checkbox" id="checkbox22"><label for="checkbox22">Tengo un pago de Claudio ya facturado sin informar, dejarla a favor</label></li>
<li><input type="checkbox" id="checkbox23"><label for="checkbox23">Rodolfo usuahia tiene a favor un evento</label></li>
<li><input type="checkbox" id="checkbox24"><label for="checkbox24">Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario</label></li>
</ul>
<h2 id="actualizo-apellidos">ACTUALIZO APELLIDOS</h2>
<p>UPDATE participantes AS p SET apellido =
(SELECT dato FROM datosxparticipantes AS dxp WHERE idevento = 2176 AND iddato = 'club' AND dxp.idinscripcion = p.idinscripcion)
WHERE idevento = 2176;</p>
<p>UPDATE datosxparticipantes
SET dato = DATE_FORMAT(STR_TO_DATE(dato, '%d/%m/%Y'), '%Y-%m-%d')
WHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;</p>
<p>UPDATE datosxparticipantes
SET dato = TRUNCATE(dato, 0)
WHERE idevento = 2176 AND iddato = 'edad' AND idinscripcion = 900072;</p>
<h2 id="botones-por-pa%C3%ADs">BOTONES POR PAÍS</h2>
<p>PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF
COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg
CHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd</p>
<p>-20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO
-30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG</p>
<h2 id="precios-de-karting">PRECIOS DE KARTING</h2>
<p>Abono Sistema Karting x 1 mes: U$D 50
https://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne</p>
<p>Abono Sistema Karting x 6 meses: U$D 250
https://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX</p>
<p>Abono Sistema Karting x 12 meses: U$D 400
https://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY</p>
<p>Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)</p>
<h2 id="mercadopago-para-ride">MERCADOPAGO PARA RIDE</h2>
<p>UPDATE precios SET idplataforma = 6, url = '<a class="button" href="https://mpago.la/2y7DW77" target="_blank">Pagar</a>' WHERE idprecio = 12;
UPDATE precios SET idplataforma = 7, url = '' WHERE idprecio = 12;</p>
<p>EX BOTÓN DE LA KAVA: <a href="https://mpago.la/1R2pnUB" target="_blank">Pagar</a>
EX BOTÓN DE RIDE: <a class="button" href="https://mpago.la/2y7DW77" target="_blank">Pagar</a></p>
<p>RIDE:
https://cronometrajeinstantaneo.com/inscripciones/ose-3-san-javier-2024/czMxNmhWTUxKVER2MEQyUUM0V0dRSkRvLzB2V25nTTZrQS9xaEZqY0tBczBoVmVXcWVmKzEvK0xmQVZGKytrUg%3D%3D</p>
<h2 id="chips-movidos">CHIPS MOVIDOS</h2>
<p>Son de ID 4 los míos (Hasta CI0200)
UPDATE <code>tags</code> SET idorganizacion = 4, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;</p>
<p>Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)
UPDATE <code>tags</code> SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;</p>
<p>Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)
UPDATE <code>tags</code> SET idorganizacion = 445, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';</p>
<p>Son de ID 41 de Ecuador-Colombia
UPDATE <code>tags</code> SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo &gt;= 7019 AND codigo &lt; 7400;
UPDATE <code>tags</code> SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo &gt; 7500 AND codigo &lt; 7821;
UPDATE <code>tags</code> SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo &gt;= 7872 AND codigo &lt;= 8350;</p>
<p>Son de ID 84 de Esquel
UPDATE <code>tags</code> SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);
UPDATE <code>tags</code> SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';</p>
<p>ES0000 a <EMAIL> (374)
UPDATE <code>tags</code> SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';
DT000 a <EMAIL> (379)
UPDATE <code>tags</code> SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';</p>
<p>Son el ID 482 de <EMAIL>
000000000000000000000001</p>
<h2 id="chips-en-karting-kartodromo">CHIPS EN KARTING KARTODROMO</h2>
<p>SELECT * FROM <code>tags</code> WHERE idtag &gt;= 36206 AND idtag &lt;= 36235 ORDER BY idtag;
https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970
https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950</p>
<p><EMAIL>
San Juan F1</p>
<p>CHILE:</p>
<p>idorganizacion = 472;</p>
<h2 id="respuesta-youtube">RESPUESTA YOUTUBE</h2>
<p>Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.</p>
<p>Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.</p>
<p>https://web.whatsapp.com/send?phone=5492944551009</p>

</body>
</html>
