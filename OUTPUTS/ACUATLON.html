<!DOCTYPE html>
<html>
<head>
<title>ACUATLON.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h2 id="feedback-2025-e-ideas-para-2026">FEEDBACK 2025 E IDEAS PARA 2026</h2>
<h3 id="estad%C3%ADsticas">ESTADÍSTICAS</h3>
<p><em>Cantidades de personas</em></p>
<ul>
<li>Personas Diferentes: 149</li>
<li>Acreditados Individuales: 113</li>
<li>Acreditados en Equipos: 18</li>
<li>Acreditados en Acuatlón Olímpico: 23</li>
<li>Acreditados en Acuatlón Posta: 15</li>
<li>Acreditados en Acuatlón Short: 14</li>
<li>Acreditados en Aguas Abiertas 1500: 27</li>
<li>Acreditados en Aguas Abiertas 4000: 29</li>
<li>Acreditados en SwimRun Dupla 10K: 3</li>
<li>Acreditados en SwimRun Individual 10K: 10</li>
<li>Acreditados en Águila Run 10K: 12</li>
<li>Acreditados en Águila Run 5K: 9</li>
<li>Inscriptos en Águila Run 21K: 3 (los pasamos a 10K)</li>
<li>Acreditados en 3 carreras: 1 (sólo una persona)</li>
<li>Acreditados en 2 carreras: 9 (sólo 6% de los inscriptos)</li>
<li>Inscriptos sin acreditarse (faltaron): 9 (6% del total)</li>
<li>Preinscriptos (no pagaron): 51 (34% de los acreditados)</li>
</ul>
<p><em>Conclusiones</em></p>
<ul>
<li>Tuvimos casi 150 personas</li>
<li>Todos los que combinaron carreras hicieron Acuatlón</li>
<li>3 combinaron junto con SwimRun</li>
<li>5 combinaron junto con Aguas Abiertas 4000m</li>
<li>1 combinaron junto con Aguas Abiertas 1500m</li>
<li>Ninguno combinó SwimRun y Aguas Abiertas, no es necesario que se puedan combiar</li>
<li>Ninguno combinó Running, son públicos diferentes</li>
<li>Ningún equipo hizo más de una carrera, no es necesario que se puedan combinar</li>
<li>Sólo hubo 3 inscriptos en 21 K, no hay interés por esta distancia en este lugar</li>
<li>Sumando a los preinscriptos interesados, suman 200 que era nuestro número objetivo</li>
<li>Acuatlón llevó 52 personas, un tercio del total y bien distribuido entre las distancias y posta</li>
<li>SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación</li>
<li>Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos</li>
<li>Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno</li>
</ul>
<h3 id="econom%C3%ADa">ECONOMÍA</h3>
<p><em>Pagos según Cajas</em></p>
<ul>
<li>MercadoPago: 90</li>
<li>Efectivo en Oceano Virgen: 14</li>
<li>Efectivo en Aquiles: 3</li>
<li>Efectivo en Meseta Wear: 1</li>
<li>MercadoPago Andy primera semana: 12</li>
<li>En efectivo a Andres: 1</li>
<li>En Acreditación: 10</li>
</ul>
<p><em>Ingresos</em></p>
<ul>
<li>Venta: $7M</li>
<li>Sponsors: $6M</li>
<li>Total: $13M</li>
</ul>
<p><em>Egresos</em></p>
<ul>
<li>Arco: $1.5M</li>
<li>Kits: $5M</li>
<li>Premios: $2.7M</li>
<li>Prensa: $1.4M</li>
<li>Promoción: $1.4M</li>
<li>Recursos: $1.6M</li>
<li>Seguridad: $0.6M</li>
<li>Comidas: $0.6M</li>
<li>Comisiones: $0.8M</li>
<li>Cronometraje: $0.4M</li>
<li>Total: $16M</li>
</ul>
<p><em>Conclusiones</em></p>
<ul>
<li>Perdimos $3M aprox. con 150 personas, cuando deberíamos estar en cero con menos participantes, sino no hay ganancias posibles.</li>
<li>No está incluído los costos de nuestro tiempo de trabajo, gastos de combustible y tanto uso de auto, etc. Si tenemos en cuenta que ambos tenemos trabajos rentables, la pérdida es mucho mayor.</li>
<li>El arco ($1.5M) ya no hay que gastarlo.</li>
<li>Los $2M de los gorros nos movieron mucho la caja, pero sobraron varios que podemos re-utilizar</li>
<li>Gastamos bastante en prensa, por ser la primera está bien, pero no creo que sea necesario hacerlo el próximo año</li>
<li>Si ahorramos en premios, prensa y arco, estamos en cero y necesitamos 200 personas el año que viene recién para recuperar la pérdida de este año y 300 participantes para tener ganancias que valgan la pena el esfuerzo</li>
<li>MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti</li>
<li>Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más</li>
<li>Falta un punto de cobro en efectivo en Piedra del Águila</li>
<li>El cobro en las acreditaciones hay que seguir manteniéndolo</li>
</ul>
<h3 id="objetivos">OBJETIVOS</h3>
<ul>
<li>El primer objetivo fue <em>divertirnos</em>. Creo que lo logramos, aunque no tanto Juli y la Colo.</li>
<li>El segundo objetivo fue <em>probar el concepto y aprender</em>. Creo que fue totalmente logrado.</li>
<li>El tercer objetivo fue <em>no perder dinero</em>. Lamentablemente no lo logramos, pero veremos si podemos hacerlo en el futuro</li>
</ul>
<h3 id="feedback">FEEDBACK</h3>
<p>Los problemas que veo son:</p>
<ul>
<li>Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.</li>
<li>No había suficiente alojamiento, se llenó la localidad por lo que no podríamos llevar el doble de gente.</li>
<li>Mucha gente vino sólo por el día, más allá del alojamiento, quicieron hacerlo así.</li>
<li>Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó varias complicaciones.</li>
<li>Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.</li>
<li>No sé si la fecha fue la correcta, hay que analizar otras opciones.</li>
<li>Si sé que el lugar fue el correcto, especialmente para la prueba de concepto que queríamos hacer.</li>
</ul>
<p>Mi propuesta para el año que viene:</p>
<p><em>Para el cronograma</em></p>
<ul>
<li>Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de todos los premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde).</li>
<li>El viernes reunión con todo el personal por grupos de responsabilidades, todos saben que hacer y no hay explicaciones el sábado.</li>
<li>El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 arrancamos y hacemos todo el evento de un tirón.</li>
<li>La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs.</li>
<li>Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche.</li>
<li>El domingo disfrutamos de correrla nosotros y viajamos de vuelta tranquilos.</li>
</ul>
<p><em>Para los circuitos</em></p>
<ul>
<li>Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente</li>
<li>Las boyas tienen que servir para SwimRun sin moverlas</li>
<li>Modificar el circuito de SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada.</li>
<li>Los circuitos de natación fueron correctos, los dejaría igual.</li>
<li>Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.</li>
</ul>
<p><em>Otros cambios</em></p>
<ul>
<li>Necesitamos asegurar que vamos a llegar a las 300 personas, hay que evaluar si es posible de alguna forma.</li>
<li>No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo).</li>
<li>El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal</li>
<li>Con 100m de vallas alcanza y hay forma de ubicarlas para que no haya que moverlas.</li>
<li>Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector.</li>
<li>Las chicas con más personal de ayuda y mejor asesoradas.</li>
<li>Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.</li>
<li>Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.</li>
<li>No ofrecer running de 21K, sólo 10K y 5K.</li>
</ul>

</body>
</html>
