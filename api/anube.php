<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);

function download_page($path) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL,$path);
    curl_setopt($ch, CURLOPT_FAILONERROR,1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION,1);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 150);
    $retValue = curl_exec($ch);
    curl_close($ch);
    return $retValue;
}

function log_anube($log) {
    global $idevento;
    file_put_contents(__DIR__.'/log_anube.csv', date("Y-m-d H:i:s").';'.$idevento.';'.$log.PHP_EOL, FILE_APPEND);
    echo date("Y-m-d H:i:s").';'.$idevento.';'.$log.'<br>';
}

function convertir_tiempo_anube($tiempo) {
    return substr($tiempo, 0, 19).'.000';
}

function validar_tiempo($tiempo) {
    return in_array(substr($tiempo, 0, 4), ['2022', '2023', '2024', '2025']);
}

// GET
$idevento = filter_input(INPUT_GET, 'idevento', FILTER_SANITIZE_SPECIAL_CHARS);
$idcontrol = filter_input(INPUT_GET, 'idcontrol', FILTER_SANITIZE_SPECIAL_CHARS);
$n_etapa = filter_input(INPUT_GET, 'n_etapa', FILTER_SANITIZE_SPECIAL_CHARS);
$waypoint_code = filter_input(INPUT_GET, 'waypoint_code', FILTER_SANITIZE_SPECIAL_CHARS);
$waypoint_id = 'id'.filter_input(INPUT_GET, 'waypoint_id', FILTER_SANITIZE_SPECIAL_CHARS);
$token = filter_input(INPUT_GET, 'token', FILTER_SANITIZE_SPECIAL_CHARS);
$idRace = filter_input(INPUT_GET, 'idRace', FILTER_SANITIZE_SPECIAL_CHARS);
$cache = filter_input(INPUT_GET, 'cache', FILTER_SANITIZE_SPECIAL_CHARS);


$url = 'http://rest.anube.es/rallyrest/default/api/waypoint_times/'
    .$idRace.'/'
    .$n_etapa.'.xml'
    .'?token='.$token
    .'&names='.$waypoint_code;

$sXML = download_page($url);
$oXML = new SimpleXMLElement($sXML);

$actualizar_cache = false;
// Recorro los participantes que son los entry
foreach($oXML->entry as $oEntry) {

    $atributes = $oEntry->attributes();
    $hours = [];
    // Recorro sus lecturas que son las hours
    foreach($oEntry->hours->attributes() as $key => $value) {
        $hours[(string) $key] = convertir_tiempo_anube((string) $value);
    }

    $idparticipante = (string) $atributes->carNo;
    $tiempo = $hours[$waypoint_id];

    if (!$idparticipante || $idparticipante > 1000)
        continue;

    if (!validar_tiempo($tiempo))
        continue;


    // Log si no existe idparticipante
    if (!contar_sql(consulta_sql(
        "SELECT idparticipante FROM participantes
        WHERE idevento = {$idevento} AND idparticipante = {$idparticipante}"))) {

        log_anube('ERROR no se encontró un participante '.$idparticipante);
    }

    $resultado_sql = consulta_sql(
        "SELECT * FROM lecturas
        WHERE tipo = 'gps'
            AND idparticipante = {$idparticipante}
            AND idcontrol = {$idcontrol}
            AND idevento = {$idevento}
            AND tiempo = '{$tiempo}'");

    if (!contar_sql($resultado_sql)) {
        consulta_sql("INSERT INTO lecturas SET
            uuid = uuid(),
            idevento = {$idevento},
            idcontrol = {$idcontrol},
            estado = 'ok',
            tiempo = '{$tiempo}',
            idparticipante = {$idparticipante},
            tipo = 'gps',
            timer = 'Stella'
        ");
        log_anube("OK: idevento = {$idevento}, idcontrol = {$idcontrol}, tiempo = '{$hours[$waypoint_id]}', idparticipante = {$idparticipante}");
        $actualizar_cache = true;
    }

}

if ($actualizar_cache)
    cache($cache);
