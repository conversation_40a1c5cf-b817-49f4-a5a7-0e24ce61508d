<?php

exit('No lo estamos usando');

require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';

$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

$resultados = array();

$resultado_sql = consulta_sql(
    "SELECT *
    FROM lecturas
    WHERE idcontrol = '".$mobile['idcontrol']."'
    ORDER BY tiempo DESC
    ");
    // LIMIT 40");
while ($lectura = array_sql($resultado_sql)) {
    $resultado = array();
    $mostrar = explode(' ', $lectura['tiempo']);
    $tiempo_listo = convertir_final(segundos_transcurridos($mobile['largada'], $lectura['tiempo']));

    $resultado['tiempo'] = $lectura['tiempo'];
    $resultado['hora'] = $mostrar[1];
    $resultado['tiempo_listo'] = $tiempo_listo;
    $resultado['idparticipante'] = $lectura['idparticipante'] ? $lectura['idparticipante'] : 'FALTA';
    $resultado['nombre'] = $nombre;
    $resultado['idlectura'] = $lectura['idlectura'];
    $resultado['resultado'] = 'Actualizado';

    $resultados[] = $resultado;
}
log_api('Actualizar; OK');
echo json_encode(array('resultados' =>$resultados));
