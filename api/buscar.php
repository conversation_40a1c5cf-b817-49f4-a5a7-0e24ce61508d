<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';
require __DIR__.'/../vendor/autoload.php';

$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);
$buscar = filter_input(INPUT_POST, 'buscar', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

$resultado = [];

$resultado_sql = consulta_sql(
    "SELECT participantes.idinscripcion, nombre, apellido, idparticipante, dato
    FROM participantes
    LEFT JOIN datosxparticipantes ON participantes.idinscripcion = datosxparticipantes.idinscripcion
        AND datosxparticipantes.iddato =
            (SELECT iddato FROM datosxeventos WHERE datosxeventos.idevento = '{$mobile['idevento']}' AND apps = 1 LIMIT 1)
    WHERE idparticipante > 0
        AND participantes.idevento = '{$mobile['idevento']}'
        AND participantes.estado != 'eliminado'
        AND (nombre LIKE '%$buscar%' OR apellido LIKE '%$buscar%' OR dato LIKE '%$buscar%')");
while ($participante = array_sql($resultado_sql)) {
    $resultado [] = [
        'idparticipante' => $participante['idparticipante'],
        'nombre' => $participante['nombre']
            .($participante['apellido'] ? ' '.$participante['apellido'] : ''),
        'extra' => $participante['dato'],
    ];

}

echo json_encode($resultado);
