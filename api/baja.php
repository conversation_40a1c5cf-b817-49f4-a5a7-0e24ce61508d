<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';
require __DIR__.'/../vendor/autoload.php';

$idlectura = filter_input(INPUT_POST, 'idlectura', FILTER_VALIDATE_INT);
$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);
$idparticipante = intval(filter_input(INPUT_POST, 'idparticipante', FILTER_SANITIZE_SPECIAL_CHARS));

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

$resultado = array();
if ($mobile['largada'] == '0000-00-00 00:00:00.000') {
    $resultado['error'] = true;
    $resultado['resultado'] = 'EVENTO SIN COMENZAR';
    log_api('Lectura; NO EMPEZÓ - '.$idparticipante.' ('.$nombre.')');

} elseif ($mobile['terminada'] != '0000-00-00 00:00:00.000') {
    $resultado['error'] = true;
    $resultado['resultado'] = 'EVENTO TERMINADO';
    log_api('Lectura; EVENTO TERMINADO - '.$idparticipante.' ('.$nombre.')');

} else {

    $resultado_lectura = consulta_sql("SELECT * FROM lecturas WHERE idlectura = '".$idlectura."' AND idcontrol = '".$mobile['idcontrol']."' LIMIT 1");
    if (!contar_sql($resultado_lectura)) {
        header("HTTP/1.1 400 Bad Request");
        echo json_encode([
            'error'     => true,
            'resultado' => "Error en idlectura"]);
        exit();
    }

    $lectura = array_sql($resultado_lectura);


    // PARCHE PARA BAJA PEDERNALES
    if (in_array($mobile['idcontrol'], [7326, 7327])) {
        $lectura = array_sql(consulta_sql(
            "SELECT *
            FROM lecturas
            WHERE idlectura = '".$idlectura."'
            LIMIT 1"));
        $mobile['idcontrol'] = $lectura['idcontrol'];
    }
    // PARCHE PARA BAJA PEDERNALES


    consulta_sql("UPDATE lecturas SET estado = 'eliminado' WHERE idlectura = '".$idlectura."' AND idcontrol = '".$mobile['idcontrol']."' LIMIT 1");
    if (afectado_sql())
        cache($mobile['evento_codigo']);

    log_api('Baja; OK Lectura '.$idlectura.' (Datos viejos:  tiempo='.$lectura['tiempo'].', idparticipante='.$lectura['idparticipante'].')');

    if ($idparticipante && in_array($mobile['tipo'], ['largada', 'final']) && $mobile['streaming']) {

        $url = URL_API.'lecturas';
        $ch = curl_init( $url );
        # Setup request to send json via POST.
        $payload = json_encode([
            'idlectura' => $resultado['idlectura'],
        ]);
        curl_setopt( $ch, CURLOPT_POSTFIELDS, $payload );
        curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
        # Return response instead of printing.
        curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
        # Send request.
        $result = curl_exec($ch);
        curl_close($ch);
        $resultado['refactor'] = $result;

    }

}
echo json_encode($resultado);
