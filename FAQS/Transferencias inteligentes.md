# TRANSFERENCIAS INTELEGENTES

## Introducción

Agregamos compatibilidad con la empresa Cucuru y su servicio de Transferencias Inteligentes, permitiendo una fácil integración con nuestros clientes y una mejor experiencia de usuario.

Tus participantes van a poder pagar mediante una transferencia bancaria a un alias exclusivamente generado para ellos. Vos vas a recibir la transferencia en tu cuenta de banco, completamente automatizada y con una comisión más baja que cualquier otra pasarela de pago, solamente el 0,7%.

## Configuración

Para configurarla tenes que seguir los siguientes pasos:

1. Ingresar a https://portalvendor.cucuru.com/registro y completar tu dirección de mail
2. Completar todos los datos del alta y subir la documentación solicitada para generar tu cuenta.
3. Ir al Menú principal (las 3 rayitas de arriba a la izquierda) y a Ajustes
4. Generar una Api Key con el botón que dice "Crear nuevo Api Key"
5. Enviar el "Código de producto" (Collector ID) y la Api Key generada a Cronometraje Instantáneo para que la configuremos en tu evento.


## Configuración de precios

Se pueden generar distintos precios, cada uno con sus rangos de fechas, cantidades de cupos y carreras a las que corresponde. De esta forma una carrera puede tener distintos precios según la fecha en que se inscriba el corredor. Con respecto al cupo sirve para cuando se quiere hacer un precio especial para los primeros inscriptos a una carrera.

De cada precio necesito la siguiente información:

- Medio de pago (de los informados en la tabla anterior)
- Precio
- Carreras en las que se aplica
- Cantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones)
- Fecha y hora desde que se habilita este precio
- Fecha y hora hasta que se deshabilita este precio

Una vez que el equipo de Cronometraje Instantáneo tenga los datos de Cucuru y los precios, haran la configuración manualmente en el sistema. Este proceso puede llevar varios días.

## Terminología

- Collector ID: es el código que utiliza Cucuru para identificar al vendedor, en este caso el organizador del evento.
- Api Key: es la clave que utiliza Cucuru para verificar los permisos de terceros (en este caso Cronometraje Instantáneo) en un Collector ID.
- Webhooks: es una url automatizada del sistema de Cronometraje Instantáneo a la que se le envían los datos de las transacciones.
- Customer o Payer: es el cliente que realiza la compra, en este caso un participante que paga la inscripción al evento.
- Collection Account: es un CVU y alias generado automáticamente por el sistema de inscripciones para que un participante pueda pagar su inscripción. Es única para cada inscripción, si una misma persona se inscribe en 2 eventos, va a tener 2 collection accounts distintos.
- Collection: es un pago realizado por un Customer a un Collection Account, es decir una transferencia bancaria a un alias o CVU. Puede ser exitosa o fallida. En caso de ser exitosa el dinero ingresa a la cuenta EN CUCURU perteneciente al Collector. En ningún momento Cronometraje Instantáneo toca el dinero ni tiene acceso a él.
- Settlement: es una transferencia que se realiza desde la cuenta del organizador dentro de Cucuru a la cuenta bancaria del mismo organizador (tiene que estar al mismo nombre fiscal) informada al registrarse. Los settlements se realizan automáticamente cada 24hs con todo el dinero acumulado en la cuenta del collector.