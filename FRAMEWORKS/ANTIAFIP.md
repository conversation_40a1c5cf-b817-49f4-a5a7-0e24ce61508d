# Framework ANTIAFIP

## Pasos para controlar

- Correr un corregir desde el mail4
- Entrar a prod y ver si tiene bien el crt e ini
- Correr un antiafip salida y ver si da error
- Si dan todos error, puede ser que el último número aprobado esté mal, revisar desde el sistema que te lleva a AFIP

---

- Tener en cuenta que todas las modificaciones de código hay que hacerlas en prod
- Si el error es NoneType' object has no attribute 'copy', es porque el último número aprobado está mal en la tabla categorias_ventas

## Comandos para verificar info

SELECT idventa, estado, estadocae, numero, total, cae, cuit, dni, idcliente FROM saas_7242.ventas WHERE idtipoventa = 1 AND numero > 26820 ORDER BY numero LIMIT 200;

## Enlaces de numeración