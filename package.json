{"name": "cronometrajeinstantaneo.app", "version": "2.16.0", "description": "App para Windows de cronometrajeinstantaneo.com", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make"}, "repository": {"type": "git", "url": "git+https://gitlab.com/cronometrajeinstantaneo/app.git"}, "author": "Cronometraje Instantáneo - <PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://gitlab.com/cronometrajeinstantaneo/app/issues"}, "homepage": "https://gitlab.com/cronometrajeinstantaneo/app#readme", "devDependencies": {"@electron-forge/cli": "^6.0.0-beta.65", "@electron-forge/maker-deb": "^6.0.0-beta.65", "@electron-forge/maker-rpm": "^6.0.0-beta.65", "@electron-forge/maker-squirrel": "^6.0.0-beta.65", "@electron-forge/maker-zip": "^6.0.0-beta.65", "electron": "^19.0.10"}, "dependencies": {"electron-squirrel-startup": "^1.0.0", "request": "^2.88.2", "serialport": "^10.4.0"}, "config": {"forge": {"packagerConfig": {}, "makers": [{"name": "@electron-forge/maker-squirrel", "config": {"name": "cronometrajeinstantaneo.app"}}, {"name": "@electron-forge/maker-zip", "platforms": ["darwin"]}, {"name": "@electron-forge/maker-deb", "config": {}}, {"name": "@electron-forge/maker-rpm", "config": {}}]}}}