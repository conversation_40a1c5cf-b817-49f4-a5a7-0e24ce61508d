<?xml version='1.0' encoding='utf-8'?>
<widget id="com.cronometrajeinstantaneo.androidapp" version="2.16.0"
    defaultlocale="es-AR"
    xmlns:gap="http://phonegap.com/ns/1.0"
    xmlns="http://www.w3.org/ns/widgets"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:cdv="http://cordova.apache.org/ns/1.0"
    >

    <name>Cronometraje Instantaneo</name>
    <description>Aplicación para cronometrar los eventos de cronometrajeinstantaneo.com</description>
    <author email="<EMAIL>" href="https://cronometrajeinstantaneo.com">cronometrajeinstantaneo.com</author>

    <content src="index.html" />
    <engine name="android" spec="10.1.2" />

    <preference name="DisallowOverscroll" value="true" />
    <preference name="android-targetSdkVersion" value="30" />

    <access origin="*" />
    <allow-navigation href="*" />
    <allow-navigation href="file:*" />
    <allow-navigation href="data:*" />
    <allow-intent href="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />

    <plugin name="cordova-plugin-splashscreen" />
    <plugin name="cordova-plugin-device" />
    <plugin name="cordova-plugin-bluetooth-serial" />
    <plugin name="cordova-plugin-ble-central" spec="1.4.3" />
    <plugin name="phonegap-plugin-barcodescanner" />
    <plugin name="cordova-plugin-androidx-adapter" />

    <platform name="android">

        <allow-intent href="market:*" />

        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application">
            <application android:usesCleartextTraffic="true" />
        </edit-config>

        <preference name="AndroidInsecureFileModeEnabled" value="true" />

        <preference name="SplashMaintainAspectRatio" value="true"/>
        <preference name="SplashShowOnlyFirstTime" value="true"/>
        <icon density="ldpi" src="res/icon/android/ldpi.png"/>
        <icon density="mdpi" src="res/icon/android/mdpi.png"/>
        <icon density="hdpi" src="res/icon/android/hdpi.png"/>
        <icon density="xhdpi" src="res/icon/android/xhdpi.png"/>
        <icon density="xxhdpi" src="res/icon/android/xxhdpi.png"/>
        <icon density="xxxhdpi" src="res/icon/android/xxxhdpi.png"/>
        <!-- Portrait -->
        <splash density="port-ldpi" src="res/screen/android/splash-port-ldpi.png"/>
        <splash density="port-mdpi" src="res/screen/android/splash-port-mdpi.png"/>
        <splash density="port-hdpi" src="res/screen/android/splash-port-hdpi.png"/>
        <splash density="port-xhdpi" src="res/screen/android/splash-port-xhdpi.png"/>
        <splash density="port-xxhdpi" src="res/screen/android/splash-port-xxhdpi.png"/>
        <splash density="port-xxxhdpi" src="res/screen/android/splash-port-xxxhdpi.png"/>
        <!-- Landscape -->
        <!--
                <splash density="land-ldpi" src="res/screen/android/splash-land-ldpi.png"/>
                <splash density="land-mdpi" src="res/screen/android/splash-land-mdpi.png"/>
                <splash density="land-hdpi" src="res/screen/android/splash-land-hdpi.png"/>
                <splash density="land-xhdpi" src="res/screen/android/splash-land-xhdpi.png"/>
                <splash density="land-xxhdpi" src="res/screen/android/splash-land-xxhdpi.png"/>
                <splash density="land-xxxhdpi" src="res/screen/android/splash-land-xxxhdpi.png"/>
        -->

    </platform>

    <platform name="ios">

        <edit-config target="NSBluetoothAlwaysUsageDescription" file="*-Info.plist" mode="merge">
            <string>Esta app utiliza Bluetooth para conectarse a las Fotocélulas de Cronometraje Instantáneo</string>
        </edit-config>

        <edit-config target="NSBluetoothPeripheralUsageDescription" file="*-Info.plist" mode="merge">
            <string>Esta app utiliza Bluetooth para conectarse a las Fotocélulas de Cronometraje Instantáneo</string>
        </edit-config>

        <edit-config target="NSCameraUsageDescription" file="*-Info.plist" mode="merge">
            <string>Esta app utiliza la cámara para escanear los códigos QR de los participantes</string>
        </edit-config>

        <icon src="res/icon/ios/icon-1024.png" width="1024" height="1024"/>
        <icon src="res/icon/ios/icon-small.png" width="29" height="29"/>
        <icon src="res/icon/ios/<EMAIL>" width="58" height="58"/>
        <icon src="res/icon/ios/<EMAIL>" width="87" height="87"/>
        <icon src="res/icon/ios/icon-small-40.png" width="40" height="40"/>
        <icon src="res/icon/ios/<EMAIL>" width="80" height="80"/>
        <icon src="res/icon/ios/<EMAIL>" width="120" height="120"/>
        <icon src="res/icon/ios/icon-small-50.png" width="50" height="50"/>
        <icon src="res/icon/ios/<EMAIL>" width="100" height="100"/>
        <icon src="res/icon/ios/icon.png" width="57" height="57"/>
        <icon src="res/icon/ios/<EMAIL>" width="114" height="114"/>
        <icon src="res/icon/ios/icon-60.png" width="60" height="60"/>
        <icon src="res/icon/ios/<EMAIL>" width="120" height="120"/>
        <icon src="res/icon/ios/<EMAIL>" width="180" height="180"/>
        <icon src="res/icon/ios/icon-72.png" width="72" height="72"/>
        <icon src="res/icon/ios/<EMAIL>" width="144" height="144"/>
        <icon src="res/icon/ios/icon-76.png" width="76" height="76"/>
        <icon src="res/icon/ios/<EMAIL>" width="152" height="152"/>
        <icon src="res/icon/ios/icon-167.png" width="167" height="167"/>
        <icon src="res/icon/ios/<EMAIL>" width="167" height="167"/>
        <!-- Storyboard portrait -->
        <splash src="res/screen/ios/Default@2x~iphone~anyany.png"/>
        <splash src="res/screen/ios/Default@2x~iphone~comany.png"/>
        <splash src="res/screen/ios/Default@3x~iphone~anyany.png"/>
        <splash src="res/screen/ios/Default@3x~iphone~comany.png"/>
        <splash src="res/screen/ios/Default@2x~ipad~anyany.png"/>
        <splash src="res/screen/ios/Default@2x~ipad~comany.png"/>

    </platform>

</widget>
