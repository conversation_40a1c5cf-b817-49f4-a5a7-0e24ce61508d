---
type: "manual"
---

- Este es un software para gestionar eventos deportivos. Los eventos se guardan en la tabla `eventos` y tienen relación con 3 "personas": el dueño del evento, el organizador y el cronometrador. No usamos foreign keys, las relaciones las controlamos en el mismo software.
- El dueño del evento es un usuario de la tabla `users` con la relación `eventos.user_id = users.id`
- Tanto el organizador como el cronometrador se guardan en la tabla `organizaciones` y pueden ser la misma persona o dos diferentes. El organizador es obligatorio para todos los eventos, mientras que el cronometrador es opcional. La relación como organizador es `eventos.idorganizacion = organizaciones.idorganizacion` y la relación como cronometrador es `eventos.idcronometrador = organizaciones.idorganizacion`.
- La tabla `organizaciones` tiene 2 campos que son los permisos. Para que una organización pueda estar como organizador debe tener `organizaciones.organiza = 1`. Para que una organización pueda estar como cronometrador debe tener `organizaciones.cronometra = 1`.
