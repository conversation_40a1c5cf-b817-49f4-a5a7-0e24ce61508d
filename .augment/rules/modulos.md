---
type: "manual"
---

Este es un sistema de gestión de eventos deportivos, contamos con varias empresas de cronometraje y organizadores de eventos en todo américa latina.

Utilizamos distintos repositorios para los distintos módulos y aplicaciones de la plataforma y todos están dentro del contexto de trabajo de este IDE. La lista completa es la siguiente:

- En la carpeta `admin` se encuentra un sistema en Laravel 8 que es el principal que usamos actualmente, pero que queremos dejar de usar. Desde este sistema se tienen que extraer los comportamientos del panel de usuario para las configuraciones generales de los eventos.
- En la carpeta `filament` se encuentra un sistema en Laravel 12 con Filament 4 que es la nueva versión del sistema principal. Actualmente lo usan pocos usuarios y está en modo beta con pocas funcionalidades.
- En la carpeta `app` se encuentra nuestra app que está hecha en Javascript y que compilamos con Phonegap Build para las versiones de Android y iOS, y también compilamos con Electron para la versión para Windows.
- En la carpeta `code` se encuentran algunas funcionalidades que tenemos en módulos más pequeños o con código PHP pequeños, principalmente en la carpeta `code/resultados` tenemos todo el cálculo y generación de los informes y los resultados de las carreras. En la carpeta `code/inscripciones` tenemos el módulo de inscripciones y pagos online. En la carpeta `code/scripts` algunos scripts puntuales y en la carpeta `code/api` nuestra API anterior (tenemos otra también en la carpeta `admin` bajo las routes de Laravel)
