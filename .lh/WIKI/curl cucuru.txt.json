{"sourceFile": "WIKI/curl cucuru.txt", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1757974772865, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1757974772865, "name": "Commit-0", "content": "curl -X POST https://api.cucuru.com/app/v1/collection/webhooks/endpoint \\\n-H \"Content-Type: application/json\" \\\n-H \"X-Cucuru-Api-Key: api-key\" \\\n-H \"X-Cucuru-Collector-id: identificador\" \\\n-d '{\n  \"url\": \"https://url_de_webhook.com/webhooks\",\n  \"header\": {\n    \"name\": \"X-header-opcional\",\n    \"value\": \"1234567890123456\"\n  }\n}'\n"}]}