## DESCRIPCIÓN DE LA TAREA

Estoy escribiendo los textos base para un sistema de gestión de eventos deportivos. Los textos son para explicar a los futuros participantes los pasos a seguir. Estos textos los organizadores del evento los van a poder editar. Todos los textos deben ser informales y dando aliento sin ser demasiado emotivos y sin dejar de ser profesionales. Los textos deben ser en html pero sin encabezado, solamente lo que va a ir dentro del <body>

---

## TEXTOS NECESARIOS

1) Texto en pantalla pre formulario: se va a mostrar antes del formulario de inscripción y tiene que dar la bienvenida y pedir que completen el siguiente formulario.

2) Texto en pantalla post formulario: es el texto que se muestra una vez que el participante ya completó el formulario y ahora debe pagar para poder confirmar su participación.

3) Texto en mail post formulario: es el texto que se envía una vez que el participante ya completó el formulario y ahora debe pagar para poder confirmar su participación. Debe ser parecido al texto en pantalla post formulario.

4) Texto en pantalla post pago: es el texto que se muestra una vez que el participante ya pagó y su inscripción está confirmada, dando las gracias y diciendo que lo esperamos en el evento.

5) Texto en mail post pago: es el texto que se muestra una vez que el participante ya pagó y su inscripción está confirmada, dando las gracias y diciendo que lo esperamos en el evento.

6) Texto en pantalla y por mail de pago rechazado: es el texto que se muestra cuando el pago fue rechazado, pidiendo que lo intente nuevamente.

7) Texto en pantalla para inscripciones cerradas: es el texto que se muestra cuando las inscripciones se encuentran cerradas.

---

## EVENTO PUNTUAL

Puntualmente estos son los datos del evento en cuestión.

{{input}}
